#!/usr/bin/env python3
"""
ChatDev Web Application Launcher
Starts the web-based ChatDev interface with 2D office automation
"""

import os
import sys
import argparse
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask', 'flask_socketio', 'openai', 'requests', 
        'colorama', 'numpy', 'tiktoken'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_openai_key():
    """Check if OpenAI API key is configured"""
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  OpenAI API key not found!")
        print("   Set your API key with:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        print("   Or create a .env file with OPENAI_API_KEY=your-api-key")
        return False
    return True

def setup_directories():
    """Ensure required directories exist"""
    directories = ['WareHouse', 'templates', 'static/css', 'static/js', 'static/images']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def open_browser(url, delay=2):
    """Open browser after a delay"""
    def open_url():
        try:
            webbrowser.open(url)
        except Exception as e:
            print(f"Could not open browser automatically: {e}")
            print(f"Please open {url} manually")
    
    Timer(delay, open_url).start()

def main():
    parser = argparse.ArgumentParser(description='ChatDev Web Application')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser automatically')
    
    args = parser.parse_args()
    
    print("🚀 Starting ChatDev Web Application...")
    print("=" * 50)
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ Dependencies OK")
    
    # Check OpenAI API key
    print("🔑 Checking OpenAI API key...")
    if not check_openai_key():
        print("⚠️  Warning: OpenAI API key not configured. Some features may not work.")
    else:
        print("✅ OpenAI API key configured")
    
    # Setup directories
    print("📁 Setting up directories...")
    setup_directories()
    print("✅ Directories ready")
    
    # Import and start the web application
    try:
        from web_app import app, socketio
        
        url = f"http://{'localhost' if args.host == '0.0.0.0' else args.host}:{args.port}"
        
        print("\n🎉 ChatDev Web Application Starting!")
        print("=" * 50)
        print(f"🌐 Dashboard URL: {url}")
        print(f"📊 API Endpoint: {url}/api/tasks")
        print(f"🔧 Visualizer: {url}/visualizer")
        print("=" * 50)
        print("\n📋 Features Available:")
        print("   • 2D Office Visualization")
        print("   • Real-time Agent Monitoring")
        print("   • Web-based Task Management")
        print("   • Automatic Software Generation")
        print("   • Download Generated Projects")
        print("\n💡 Usage:")
        print("   1. Open the dashboard in your browser")
        print("   2. Enter a project description")
        print("   3. Watch AI agents collaborate in real-time")
        print("   4. Download your generated software")
        print("\n🛑 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Open browser automatically unless disabled
        if not args.no_browser:
            print(f"🌐 Opening browser to {url}...")
            open_browser(url)
        
        # Start the Flask-SocketIO server
        socketio.run(
            app, 
            host=args.host, 
            port=args.port, 
            debug=args.debug,
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except ImportError as e:
        print(f"❌ Failed to import web application: {e}")
        print("   Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 ChatDev Web Application stopped.")
        print("   Thank you for using ChatDev!")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
