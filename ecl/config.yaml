experience:
  reap_zombie: True
  threshold: 0
  upper_limit: 10

codes:
  tmp_directory: "tmp_codes"
  main_script: "main.py"

embedding_method: "OpenAI"

retrieval:
  top_k_code: 1 # top k target code
  top_k_text: 1 # top k instructionstar

  searchcode_thresh: 0 # similarity threshold between text query and instructionstar, search for targetcode  
  searchtext_thresh: 0 # similarity threshold between code query and sourcecode, search for instructionstar
