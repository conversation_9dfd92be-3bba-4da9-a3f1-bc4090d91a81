#!/usr/bin/env python3
"""
Test script for ChatDev Web Deployment
Verifies that all components are working correctly
"""

import os
import sys
import time
import requests
import threading
from unittest.mock import patch

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import flask
        import flask_socketio
        import openai
        from chatdev.chat_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
        from camel.typing import ModelType
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("🔍 Testing file structure...")
    
    required_files = [
        'web_app.py',
        'start_web.py',
        'templates/dashboard.html',
        'templates/api_docs.html',
        'static/css/dashboard.css',
        'static/js/dashboard.js',
        'requirements.txt',
        'CompanyConfig/Default/ChatChainConfig.json',
        'CompanyConfig/Default/PhaseConfig.json',
        'CompanyConfig/Default/RoleConfig.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files present")
    return True

def test_web_app_startup():
    """Test that the web app can start without errors"""
    print("🔍 Testing web app startup...")
    
    try:
        # Mock the OpenAI API key for testing
        os.environ['OPENAI_API_KEY'] = 'test-key-for-startup-test'
        
        from web_app import app, task_manager
        
        # Test that the app can be created
        assert app is not None
        assert task_manager is not None
        
        # Test that routes are registered
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        expected_routes = ['/', '/api/tasks', '/api/agents/status', '/api/docs']
        
        for route in expected_routes:
            if route not in routes:
                print(f"❌ Missing route: {route}")
                return False
        
        print("✅ Web app startup successful")
        return True
        
    except Exception as e:
        print(f"❌ Web app startup error: {e}")
        return False

def test_task_manager():
    """Test the task manager functionality"""
    print("🔍 Testing task manager...")
    
    try:
        from web_app import TaskManager
        
        # Create a test task manager
        tm = TaskManager()
        
        # Test task creation
        task_id = tm.create_task("Test project", "Create a simple calculator", "Default")
        assert task_id is not None
        
        # Test task retrieval
        task = tm.get_task(task_id)
        assert task is not None
        assert task['project_name'] == "Test project"
        assert task['status'] == "queued"
        
        # Test task update
        tm.update_task_status(task_id, "running", progress=50)
        updated_task = tm.get_task(task_id)
        assert updated_task['status'] == "running"
        assert updated_task['progress'] == 50
        
        print("✅ Task manager working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Task manager error: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints (requires running server)"""
    print("🔍 Testing API endpoints...")
    
    # This test requires the server to be running
    # We'll just check if the endpoints are defined correctly
    try:
        from web_app import app
        
        with app.test_client() as client:
            # Test main dashboard
            response = client.get('/')
            assert response.status_code == 200
            
            # Test API docs
            response = client.get('/api/docs')
            assert response.status_code == 200
            
            # Test agents status
            response = client.get('/api/agents/status')
            assert response.status_code == 200
            
            # Test configs endpoint
            response = client.get('/api/configs')
            assert response.status_code == 200
            
        print("✅ API endpoints responding correctly")
        return True
        
    except Exception as e:
        print(f"❌ API endpoint error: {e}")
        return False

def test_configuration_files():
    """Test that configuration files are valid JSON"""
    print("🔍 Testing configuration files...")
    
    config_files = [
        'CompanyConfig/Default/ChatChainConfig.json',
        'CompanyConfig/Default/PhaseConfig.json',
        'CompanyConfig/Default/RoleConfig.json'
    ]
    
    try:
        import json
        
        for config_file in config_files:
            with open(config_file, 'r') as f:
                json.load(f)  # This will raise an exception if invalid JSON
        
        print("✅ All configuration files are valid JSON")
        return True
        
    except Exception as e:
        print(f"❌ Configuration file error: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("🧪 ChatDev Web Deployment Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("File Structure Test", test_file_structure),
        ("Configuration Test", test_configuration_files),
        ("Web App Startup Test", test_web_app_startup),
        ("Task Manager Test", test_task_manager),
        ("API Endpoints Test", test_api_endpoints),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! ChatDev web deployment is ready.")
        print("\n🚀 To start the web application:")
        print("   python start_web.py")
        print("\n🌐 Then visit: http://localhost:5000")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
