.container {
    margin-top: 20px;
    margin-bottom: 20px;
    width: 2500px;
    height: 700px;
    border: 1px solid black;
    overflow-y: scroll;
    background-color: white;
    position: relative;
}

.message-container {
  position: relative;
  display: flex;
  margin: 20px;
  max-width: 95%;
  word-wrap: break-word;
  padding-top: 0px; /* Add space for the button */
}


.message-text {
    background-color: #D2D4D3;
    border-radius: 10px;
    padding: 8px;
    margin-left: 40px;
    font-size: 10px;
    width: 100%;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: absolute;
    top: 0px;
    left: -30px;
    margin-left: 20px;
    background-color: green;
    background-size: cover;
}

.role {
    font-size: 12px;
    font-weight: bold;
    position: absolute;
    bottom: 0;
    top: -30px;
    margin-top: 10px;
    margin-left: 40px;
}

.code-block pre {
    margin: 0;
}

.dark {
    color: #000000;
}

.line-numbers .line-numbers-rows {
    border-right-color: #44475a;
    display: block; /* Add this to ensure line numbers are displayed */
}

.code-block-header {
    background-color: #5b5656;
    color: #ffffff;
    padding: 5px;
    font-size: 14px;
    font-weight: bold;
}


.code-block {
    background-color: #000000 !important;
    border-radius: 4px;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
    color: #000000; /* Add text color for syntax highlighting */
}

table {
  border-collapse: collapse;
  width: 100%;
}

th, td {
  border: 1px solid black;
  padding: 8px;
  text-align: left;
  white-space: pre-line;
}

.expand-button {
  position: absolute;
  top: 3px;
  right: 65px;
  border-radius: 4px;
  background-color: #95A1A1;
  color: #f8f8f2;
  border: none;
  padding: 5px;
  cursor: pointer;
  font-size: 8px;
  width: 50px;
  height: 20px;
  line-height: 10px;
  font-weight: bold;
}

.expand-button:hover {
  background-color: #6B9297; 
}

.copy-button {
    position: absolute;
    top: 3px;
    right: 3px;
    background-color: #A9A9A7;
    color: #f8f8f2;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    width: 55px;
    height: 20px;
    line-height: 10px;
    font-size: 8px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.copy-button:hover {
  background-color: #866753; 
}
