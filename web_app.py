#!/usr/bin/env python3
"""
ChatDev Web Application
A web-based interface for the ChatDev multi-agent software development system.
Provides REST API endpoints and a 2D office visualization interface.
"""

import os
import json
import uuid
import threading
import time
from datetime import datetime
from flask import Flask, request, jsonify, render_template, send_file
from flask_socketio import Socket<PERSON>, emit
import logging

from chatdev.chat_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from camel.typing import ModelType
from universal_automation import UniversalTaskAutomation, CustomWorker, TaskTemplate, TaskCategory, MBTIType
from office_builder import OfficeBuilder, OfficeTheme, WorkstationType
import sys
import importlib.util

# Initialize Flask app with Socket<PERSON> for real-time communication
app = Flask(__name__)
app.config['SECRET_KEY'] = 'chatdev_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables for task management
active_tasks = {}
task_queue = []
agents_status = {
    "CEO": {"status": "idle", "current_task": None},
    "CTO": {"status": "idle", "current_task": None},
    "CPO": {"status": "idle", "current_task": None},
    "Programmer": {"status": "idle", "current_task": None},
    "Code Reviewer": {"status": "idle", "current_task": None},
    "Tester": {"status": "idle", "current_task": None},
    "Designer": {"status": "idle", "current_task": None}
}

# Initialize universal automation system
universal_automation = UniversalTaskAutomation()
office_builder = OfficeBuilder()

# Create default workers if none exist
default_workers = CustomWorker.create_default_workers()
for worker in default_workers:
    universal_automation.workers[worker.id] = worker

class TaskManager:
    def __init__(self):
        self.tasks = {}
        self.lock = threading.Lock()
    
    def create_task(self, task_description, project_name, config="Default"):
        task_id = str(uuid.uuid4())
        task = {
            "id": task_id,
            "description": task_description,
            "project_name": project_name,
            "config": config,
            "status": "queued",
            "created_at": datetime.now().isoformat(),
            "started_at": None,
            "completed_at": None,
            "progress": 0,
            "current_phase": None,
            "log_file": None,
            "output_directory": None,
            "error": None
        }
        
        with self.lock:
            self.tasks[task_id] = task
            task_queue.append(task_id)
        
        # Emit task created event
        socketio.emit('task_created', task)
        return task_id
    
    def update_task_status(self, task_id, status, **kwargs):
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["status"] = status
                for key, value in kwargs.items():
                    self.tasks[task_id][key] = value
                
                # Emit task updated event
                socketio.emit('task_updated', self.tasks[task_id])
    
    def get_task(self, task_id):
        return self.tasks.get(task_id)
    
    def get_all_tasks(self):
        return list(self.tasks.values())

task_manager = TaskManager()

def execute_universal_task(task_id):
    """Execute a universal automation task"""
    task = universal_automation.active_tasks.get(task_id)
    if not task:
        return

    try:
        # Update task status
        task['status'] = 'running'
        task['started_at'] = datetime.now().isoformat()
        socketio.emit('task_updated', task)

        # Get assigned workers
        assigned_workers = [universal_automation.workers[worker_id]
                          for worker_id in task['assigned_workers']
                          if worker_id in universal_automation.workers]

        # Execute each step with personality-aware prompts
        for i, step in enumerate(task['steps']):
            task['current_step'] = i
            task['progress'] = int((i / len(task['steps'])) * 100)
            task['current_phase'] = step['phase']

            # Find suitable worker for this step
            suitable_worker = None
            for worker in assigned_workers:
                if step['role'].lower() in worker.role.lower() or \
                   any(skill in step['role'].lower() for skill in worker.skills):
                    suitable_worker = worker
                    break

            if suitable_worker:
                # Generate personality-aware prompt
                context = f"Step: {step['description']}\nTask: {task['name']}\nInputs: {task['inputs']}"
                prompt = universal_automation.get_worker_prompt(suitable_worker.id, context)

                # Update agent status
                socketio.emit('agent_status_update', {
                    "agent": suitable_worker.name,
                    "status": "working",
                    "task_id": task_id,
                    "step": step['phase']
                })

                # Simulate work (in real implementation, this would call LLM)
                time.sleep(2)  # Simulate processing time

                # Reset agent status
                socketio.emit('agent_status_update', {
                    "agent": suitable_worker.name,
                    "status": "idle",
                    "task_id": None
                })

            # Emit progress update
            socketio.emit('task_updated', task)
            socketio.emit('phase_update', {
                "task_id": task_id,
                "phase": step['phase'],
                "progress": task['progress']
            })

        # Task completed
        task['status'] = 'completed'
        task['progress'] = 100
        task['completed_at'] = datetime.now().isoformat()
        task['current_phase'] = 'Completed'

        socketio.emit('task_updated', task)

    except Exception as e:
        task['status'] = 'failed'
        task['error'] = str(e)
        task['completed_at'] = datetime.now().isoformat()
        socketio.emit('task_updated', task)

def execute_chatdev_task(task_id):
    """Execute a ChatDev task in a separate thread"""
    task = task_manager.get_task(task_id)
    if not task:
        return
    
    try:
        # Update task status to running
        task_manager.update_task_status(
            task_id, 
            "running", 
            started_at=datetime.now().isoformat()
        )
        
        # Update agent status
        socketio.emit('agent_status_update', {
            "agent": "CEO", 
            "status": "working", 
            "task_id": task_id
        })
        
        # Set up ChatDev configuration
        config_path = f"CompanyConfig/{task['config']}/ChatChainConfig.json"
        config_phase_path = f"CompanyConfig/{task['config']}/PhaseConfig.json"
        config_role_path = f"CompanyConfig/{task['config']}/RoleConfig.json"
        
        # Create ChatChain instance
        chat_chain = ChatChain(
            config_path=config_path,
            config_phase_path=config_phase_path,
            config_role_path=config_role_path,
            task_prompt=task['description'],
            project_name=task['project_name'],
            org_name="WebOrganization",
            model_type=ModelType.GPT_3_5_TURBO
        )
        
        # Set up logging for this task
        log_file = f"WareHouse/{task['project_name']}_WebOrganization_{int(time.time())}.log"
        task_manager.update_task_status(task_id, "running", log_file=log_file)
        
        # Execute the development process
        chat_chain.pre_processing()
        task_manager.update_task_status(task_id, "running", progress=10, current_phase="Preprocessing")
        
        chat_chain.make_recruitment()
        task_manager.update_task_status(task_id, "running", progress=20, current_phase="Recruitment")
        
        # Execute the main chain with progress updates
        phases = ["DemandAnalysis", "LanguageChoose", "Coding", "CodeReview", "Test", "Manual"]
        for i, phase in enumerate(phases):
            progress = 30 + (i * 10)
            task_manager.update_task_status(task_id, "running", progress=progress, current_phase=phase)
            socketio.emit('phase_update', {
                "task_id": task_id,
                "phase": phase,
                "progress": progress
            })
        
        chat_chain.execute_chain()
        
        # Task completed successfully
        output_dir = f"WareHouse/{task['project_name']}_WebOrganization_{int(time.time())}"
        task_manager.update_task_status(
            task_id,
            "completed",
            progress=100,
            completed_at=datetime.now().isoformat(),
            output_directory=output_dir,
            current_phase="Completed"
        )
        
        # Reset agent status
        socketio.emit('agent_status_update', {
            "agent": "CEO", 
            "status": "idle", 
            "task_id": None
        })
        
    except Exception as e:
        # Task failed
        task_manager.update_task_status(
            task_id,
            "failed",
            error=str(e),
            completed_at=datetime.now().isoformat()
        )
        
        # Reset agent status
        socketio.emit('agent_status_update', {
            "agent": "CEO", 
            "status": "idle", 
            "task_id": None
        })

# REST API Endpoints

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/docs')
def api_docs():
    """API documentation page"""
    return render_template('api_docs.html')

@app.route('/api/tasks', methods=['POST'])
def create_task():
    """Create a new development task"""
    data = request.json
    
    if not data or 'description' not in data or 'project_name' not in data:
        return jsonify({"error": "Missing required fields: description, project_name"}), 400
    
    task_id = task_manager.create_task(
        task_description=data['description'],
        project_name=data['project_name'],
        config=data.get('config', 'Default')
    )
    
    # Start task execution in background thread
    thread = threading.Thread(target=execute_chatdev_task, args=(task_id,))
    thread.daemon = True
    thread.start()
    
    return jsonify({"task_id": task_id, "status": "created"}), 201

@app.route('/api/tasks', methods=['GET'])
def get_tasks():
    """Get all tasks"""
    return jsonify(task_manager.get_all_tasks())

@app.route('/api/tasks/<task_id>', methods=['GET'])
def get_task(task_id):
    """Get specific task details"""
    task = task_manager.get_task(task_id)
    if not task:
        return jsonify({"error": "Task not found"}), 404
    return jsonify(task)

@app.route('/api/tasks/<task_id>/download', methods=['GET'])
def download_task_output(task_id):
    """Download the generated software for a completed task"""
    task = task_manager.get_task(task_id)
    if not task:
        return jsonify({"error": "Task not found"}), 404
    
    if task['status'] != 'completed' or not task['output_directory']:
        return jsonify({"error": "Task not completed or no output available"}), 400
    
    # Create a zip file of the output directory
    import zipfile
    import tempfile
    
    temp_zip = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
    with zipfile.ZipFile(temp_zip.name, 'w') as zipf:
        output_dir = task['output_directory']
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, output_dir)
                zipf.write(file_path, arcname)
    
    return send_file(temp_zip.name, as_attachment=True, download_name=f"{task['project_name']}.zip")

@app.route('/api/agents/status', methods=['GET'])
def get_agents_status():
    """Get current status of all agents"""
    return jsonify(agents_status)

@app.route('/visualizer')
def visualizer():
    """Legacy visualizer route for compatibility"""
    return render_template('visualizer.html')

@app.route('/api/configs', methods=['GET'])
def get_configs():
    """Get available configuration options"""
    configs = []
    config_dir = "CompanyConfig"
    if os.path.exists(config_dir):
        for item in os.listdir(config_dir):
            if os.path.isdir(os.path.join(config_dir, item)):
                configs.append(item)
    return jsonify(configs)

# New Universal Automation Endpoints

@app.route('/office')
def office_management():
    """Office management interface"""
    return render_template('office_management.html')

@app.route('/api/workers', methods=['GET'])
def get_workers():
    """Get all custom workers"""
    workers = [worker.__dict__ for worker in universal_automation.workers.values()]
    # Convert personality to dict for JSON serialization
    for worker in workers:
        if hasattr(worker['personality'], '__dict__'):
            worker['personality'] = worker['personality'].__dict__
    return jsonify(workers)

@app.route('/api/workers', methods=['POST'])
def create_worker():
    """Create a new custom worker"""
    data = request.json

    if not data or 'name' not in data or 'role' not in data:
        return jsonify({"error": "Missing required fields: name, role"}), 400

    try:
        # Parse MBTI type
        mbti_type = MBTIType(data.get('mbti_type', 'ENFP'))

        worker = universal_automation.create_custom_worker(
            name=data['name'],
            role=data['role'],
            department=data.get('department', 'General'),
            skills=data.get('skills', []),
            mbti_type=mbti_type,
            avatar_emoji=data.get('avatar_emoji', '👤')
        )

        # Update communication style if provided
        if 'communication_style' in data:
            worker.personality.communication_style = data['communication_style']

        if 'experience_level' in data:
            worker.experience_level = data['experience_level']

        # Emit worker created event
        socketio.emit('worker_created', worker.__dict__)

        return jsonify(worker.__dict__), 201

    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route('/api/workers/<worker_id>', methods=['DELETE'])
def delete_worker(worker_id):
    """Delete a custom worker"""
    if worker_id in universal_automation.workers:
        worker = universal_automation.workers[worker_id]
        del universal_automation.workers[worker_id]
        socketio.emit('worker_deleted', {"worker_id": worker_id, "name": worker.name})
        return jsonify({"message": "Worker deleted successfully"})
    else:
        return jsonify({"error": "Worker not found"}), 404

@app.route('/api/templates', methods=['GET'])
def get_task_templates():
    """Get all task templates"""
    templates = [template.__dict__ for template in universal_automation.templates.values()]
    # Convert enums to strings for JSON serialization
    for template in templates:
        if hasattr(template['category'], 'value'):
            template['category'] = template['category'].value
    return jsonify(templates)

@app.route('/api/templates/<template_id>/execute', methods=['POST'])
def execute_template(template_id):
    """Execute a task template"""
    data = request.json

    if template_id not in universal_automation.templates:
        return jsonify({"error": "Template not found"}), 404

    try:
        task = universal_automation.create_task_from_template(
            template_id=template_id,
            inputs=data.get('inputs', {})
        )

        # Start task execution in background thread
        thread = threading.Thread(target=execute_universal_task, args=(task['id'],))
        thread.daemon = True
        thread.start()

        return jsonify(task), 201

    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route('/api/office/layout', methods=['GET'])
def get_office_layout():
    """Get current office layout"""
    # Return default layout if none exists
    if not office_builder.layouts:
        default_layout = office_builder.create_office_layout("My Office", OfficeTheme.MODERN)
        return jsonify(default_layout.to_dict())

    # Return the first layout (in a real app, you'd track user's current layout)
    layout_id = list(office_builder.layouts.keys())[0]
    return jsonify(office_builder.layouts[layout_id].to_dict())

@app.route('/api/office/layout', methods=['POST'])
def update_office_layout():
    """Update office layout"""
    data = request.json

    try:
        layout_id = office_builder.import_layout(data)
        layout = office_builder.layouts[layout_id]

        socketio.emit('office_updated', layout.to_dict())
        return jsonify(layout.to_dict())

    except Exception as e:
        return jsonify({"error": str(e)}), 400

# WebSocket Events for Real-time Communication

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'data': 'Connected to ChatDev Web Interface'})
    emit('agents_status', agents_status)

@socketio.on('request_task_updates')
def handle_task_updates():
    """Send current task status to client"""
    emit('task_list', task_manager.get_all_tasks())

if __name__ == '__main__':
    # Ensure WareHouse directory exists
    os.makedirs('WareHouse', exist_ok=True)
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Starting ChatDev Web Application...")
    print("📊 Dashboard: http://localhost:5000")
    print("🔌 API Docs: http://localhost:5000/api/tasks")
    
    # Run the application
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
