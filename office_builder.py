#!/usr/bin/env python3
"""
Customizable Office Builder
Allows users to design their own virtual office layout and environment.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

class OfficeTheme(Enum):
    """Available office themes"""
    MODERN = "modern"
    COZY = "cozy"
    MINIMALIST = "minimalist"
    CREATIVE = "creative"
    CORPORATE = "corporate"
    FUTURISTIC = "futuristic"
    NATURE = "nature"
    RETRO = "retro"

class WorkstationType(Enum):
    """Types of workstations available"""
    DESK = "desk"
    MEETING_ROOM = "meeting_room"
    CREATIVE_SPACE = "creative_space"
    QUIET_ZONE = "quiet_zone"
    COLLABORATION_AREA = "collaboration_area"
    BREAK_ROOM = "break_room"
    EXECUTIVE_OFFICE = "executive_office"
    RESEARCH_LAB = "research_lab"
    PRESENTATION_AREA = "presentation_area"
    PHONE_BOOTH = "phone_booth"

@dataclass
class WorkstationConfig:
    """Configuration for a workstation in the office"""
    id: str
    name: str
    type: WorkstationType
    position: Tuple[int, int]  # (x, y) coordinates in grid
    size: Tuple[int, int]      # (width, height) in grid units
    assigned_worker_id: Optional[str]
    equipment: List[str]       # Available equipment/tools
    capacity: int              # How many workers can use this space
    is_private: bool
    decoration: Dict[str, Any] # Custom decorations and styling
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)

@dataclass
class OfficeLayout:
    """Represents the complete office layout"""
    id: str
    name: str
    theme: OfficeTheme
    grid_size: Tuple[int, int]  # (width, height) of the office grid
    workstations: Dict[str, WorkstationConfig]
    decorations: List[Dict[str, Any]]  # Office-wide decorations
    background_music: Optional[str]
    lighting: str  # bright, dim, natural, etc.
    created_at: str
    last_modified: str
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['workstations'] = {k: v.to_dict() for k, v in self.workstations.items()}
        return data

class OfficeBuilder:
    """Main class for building and managing custom offices"""
    
    def __init__(self):
        self.layouts: Dict[str, OfficeLayout] = {}
        self.theme_configs = self._load_theme_configs()
        self.workstation_templates = self._load_workstation_templates()
    
    def _load_theme_configs(self) -> Dict[str, Dict]:
        """Load theme-specific configurations"""
        return {
            OfficeTheme.MODERN.value: {
                "colors": {
                    "primary": "#2563eb",
                    "secondary": "#f8fafc",
                    "accent": "#0ea5e9",
                    "background": "#ffffff"
                },
                "fonts": "Inter, sans-serif",
                "decorations": ["plants", "modern_art", "glass_partitions"],
                "lighting": "bright",
                "music": "ambient_electronic"
            },
            OfficeTheme.COZY.value: {
                "colors": {
                    "primary": "#92400e",
                    "secondary": "#fef3c7",
                    "accent": "#d97706",
                    "background": "#fffbeb"
                },
                "fonts": "Georgia, serif",
                "decorations": ["books", "warm_lighting", "wooden_furniture"],
                "lighting": "warm",
                "music": "acoustic_cafe"
            },
            OfficeTheme.CREATIVE.value: {
                "colors": {
                    "primary": "#7c3aed",
                    "secondary": "#faf5ff",
                    "accent": "#a855f7",
                    "background": "#ffffff"
                },
                "fonts": "Poppins, sans-serif",
                "decorations": ["colorful_art", "inspiration_boards", "creative_tools"],
                "lighting": "colorful",
                "music": "upbeat_creative"
            },
            OfficeTheme.NATURE.value: {
                "colors": {
                    "primary": "#059669",
                    "secondary": "#ecfdf5",
                    "accent": "#10b981",
                    "background": "#f0fdf4"
                },
                "fonts": "Nunito, sans-serif",
                "decorations": ["plants", "natural_wood", "stone_elements"],
                "lighting": "natural",
                "music": "nature_sounds"
            }
        }
    
    def _load_workstation_templates(self) -> Dict[str, Dict]:
        """Load workstation templates with default configurations"""
        return {
            WorkstationType.DESK.value: {
                "name": "Standard Desk",
                "size": (2, 2),
                "equipment": ["computer", "phone", "desk_lamp"],
                "capacity": 1,
                "is_private": False,
                "default_decoration": {"style": "professional", "items": ["monitor", "keyboard"]}
            },
            WorkstationType.EXECUTIVE_OFFICE.value: {
                "name": "Executive Office",
                "size": (3, 3),
                "equipment": ["computer", "phone", "meeting_table", "bookshelf"],
                "capacity": 1,
                "is_private": True,
                "default_decoration": {"style": "luxury", "items": ["leather_chair", "awards", "plants"]}
            },
            WorkstationType.CREATIVE_SPACE.value: {
                "name": "Creative Studio",
                "size": (3, 2),
                "equipment": ["drawing_tablet", "design_software", "inspiration_board"],
                "capacity": 2,
                "is_private": False,
                "default_decoration": {"style": "artistic", "items": ["easel", "color_swatches", "sketches"]}
            },
            WorkstationType.MEETING_ROOM.value: {
                "name": "Meeting Room",
                "size": (4, 3),
                "equipment": ["projector", "whiteboard", "conference_table"],
                "capacity": 8,
                "is_private": True,
                "default_decoration": {"style": "professional", "items": ["chairs", "presentation_screen"]}
            },
            WorkstationType.RESEARCH_LAB.value: {
                "name": "Research Lab",
                "size": (3, 3),
                "equipment": ["multiple_monitors", "data_servers", "analysis_tools"],
                "capacity": 2,
                "is_private": False,
                "default_decoration": {"style": "technical", "items": ["charts", "data_visualizations", "reference_books"]}
            },
            WorkstationType.BREAK_ROOM.value: {
                "name": "Break Room",
                "size": (3, 2),
                "equipment": ["coffee_machine", "microwave", "comfortable_seating"],
                "capacity": 6,
                "is_private": False,
                "default_decoration": {"style": "relaxed", "items": ["plants", "magazines", "games"]}
            }
        }
    
    def create_office_layout(self, name: str, theme: OfficeTheme, 
                           grid_size: Tuple[int, int] = (12, 8)) -> OfficeLayout:
        """Create a new office layout"""
        layout_id = str(uuid.uuid4())
        
        layout = OfficeLayout(
            id=layout_id,
            name=name,
            theme=theme,
            grid_size=grid_size,
            workstations={},
            decorations=[],
            background_music=self.theme_configs[theme.value].get("music"),
            lighting=self.theme_configs[theme.value].get("lighting", "bright"),
            created_at=datetime.now().isoformat(),
            last_modified=datetime.now().isoformat()
        )
        
        self.layouts[layout_id] = layout
        return layout
    
    def add_workstation(self, layout_id: str, workstation_type: WorkstationType,
                       position: Tuple[int, int], custom_name: Optional[str] = None) -> WorkstationConfig:
        """Add a workstation to an office layout"""
        if layout_id not in self.layouts:
            raise ValueError(f"Layout {layout_id} not found")
        
        layout = self.layouts[layout_id]
        template = self.workstation_templates[workstation_type.value]
        
        workstation_id = str(uuid.uuid4())
        workstation = WorkstationConfig(
            id=workstation_id,
            name=custom_name or template["name"],
            type=workstation_type,
            position=position,
            size=template["size"],
            assigned_worker_id=None,
            equipment=template["equipment"].copy(),
            capacity=template["capacity"],
            is_private=template["is_private"],
            decoration=template["default_decoration"].copy()
        )
        
        # Check if position is available
        if self._is_position_occupied(layout, position, workstation.size):
            raise ValueError(f"Position {position} is already occupied")
        
        layout.workstations[workstation_id] = workstation
        layout.last_modified = datetime.now().isoformat()
        
        return workstation
    
    def _is_position_occupied(self, layout: OfficeLayout, position: Tuple[int, int], 
                            size: Tuple[int, int]) -> bool:
        """Check if a position is already occupied by another workstation"""
        x, y = position
        width, height = size
        
        for workstation in layout.workstations.values():
            wx, wy = workstation.position
            wwidth, wheight = workstation.size
            
            # Check for overlap
            if (x < wx + wwidth and x + width > wx and 
                y < wy + wheight and y + height > wy):
                return True
        
        return False
    
    def assign_worker_to_workstation(self, layout_id: str, workstation_id: str, 
                                   worker_id: str) -> bool:
        """Assign a worker to a specific workstation"""
        if layout_id not in self.layouts:
            return False
        
        layout = self.layouts[layout_id]
        if workstation_id not in layout.workstations:
            return False
        
        workstation = layout.workstations[workstation_id]
        workstation.assigned_worker_id = worker_id
        layout.last_modified = datetime.now().isoformat()
        
        return True
    
    def customize_workstation(self, layout_id: str, workstation_id: str, 
                            customizations: Dict[str, Any]) -> bool:
        """Apply custom decorations and equipment to a workstation"""
        if layout_id not in self.layouts:
            return False
        
        layout = self.layouts[layout_id]
        if workstation_id not in layout.workstations:
            return False
        
        workstation = layout.workstations[workstation_id]
        
        # Update equipment if provided
        if "equipment" in customizations:
            workstation.equipment = customizations["equipment"]
        
        # Update decorations if provided
        if "decoration" in customizations:
            workstation.decoration.update(customizations["decoration"])
        
        # Update name if provided
        if "name" in customizations:
            workstation.name = customizations["name"]
        
        layout.last_modified = datetime.now().isoformat()
        return True
    
    def get_office_css(self, layout_id: str) -> str:
        """Generate CSS for the office layout"""
        if layout_id not in self.layouts:
            return ""
        
        layout = self.layouts[layout_id]
        theme_config = self.theme_configs[layout.theme.value]
        
        css = f"""
        .office-container-{layout_id} {{
            background: {theme_config['colors']['background']};
            font-family: {theme_config['fonts']};
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }}
        
        .office-grid-{layout_id} {{
            display: grid;
            grid-template-columns: repeat({layout.grid_size[0]}, 1fr);
            grid-template-rows: repeat({layout.grid_size[1]}, 1fr);
            gap: 5px;
            height: 100%;
            min-height: 600px;
        }}
        
        .workstation-{layout_id} {{
            background: {theme_config['colors']['secondary']};
            border: 2px solid {theme_config['colors']['primary']};
            border-radius: 8px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }}
        
        .workstation-{layout_id}:hover {{
            background: {theme_config['colors']['accent']};
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }}
        
        .workstation-{layout_id}.occupied {{
            background: linear-gradient(135deg, {theme_config['colors']['accent']}, {theme_config['colors']['primary']});
            color: white;
        }}
        """
        
        return css
    
    def export_layout(self, layout_id: str) -> Dict:
        """Export office layout as JSON"""
        if layout_id not in self.layouts:
            return {}
        
        return self.layouts[layout_id].to_dict()
    
    def import_layout(self, layout_data: Dict) -> str:
        """Import office layout from JSON"""
        layout_id = layout_data.get('id', str(uuid.uuid4()))
        
        # Reconstruct workstations
        workstations = {}
        for ws_id, ws_data in layout_data.get('workstations', {}).items():
            workstations[ws_id] = WorkstationConfig(**ws_data)
        
        layout = OfficeLayout(
            id=layout_id,
            name=layout_data['name'],
            theme=OfficeTheme(layout_data['theme']),
            grid_size=tuple(layout_data['grid_size']),
            workstations=workstations,
            decorations=layout_data.get('decorations', []),
            background_music=layout_data.get('background_music'),
            lighting=layout_data.get('lighting', 'bright'),
            created_at=layout_data.get('created_at', datetime.now().isoformat()),
            last_modified=datetime.now().isoformat()
        )
        
        self.layouts[layout_id] = layout
        return layout_id

# Export main classes
__all__ = ['OfficeBuilder', 'OfficeLayout', 'WorkstationConfig', 'OfficeTheme', 'WorkstationType']
