# ChatDev Web Deployment Guide

## 🎯 Overview

This guide explains how to deploy ChatDev as a web application with a 2D office automation interface. The transformation includes:

- **Web-based interface** instead of command-line
- **2D office visualization** showing AI agents at work
- **Real-time monitoring** of development progress
- **REST API** for programmatic access
- **Automated task management** with queue system

## 🏗️ Architecture

### **Before (CLI)**
```
User → run.py → Chat<PERSON>hain → Agents → Output Files
```

### **After (Web)**
```
User → Web Dashboard → REST API → Task Queue → ChatChain → Agents → Real-time Updates → Download
                                      ↓
                               2D Office Visualization
```

## 📁 New File Structure

```
ChatDev/
├── 🌐 Web Application
│   ├── web_app.py              # Main Flask application
│   ├── start_web.py            # Startup script
│   ├── templates/              # HTML templates
│   │   ├── dashboard.html      # Main 2D office interface
│   │   └── api_docs.html       # API documentation
│   └── static/                 # CSS, JS, images
│       ├── css/dashboard.css   # 2D office styling
│       └── js/dashboard.js     # Real-time interactions
├── 🤖 Core Framework (Unchanged)
│   ├── chatdev/               # Core ChatDev modules
│   ├── camel/                 # Agent communication
│   ├── ecl/                   # Experience learning
│   └── CompanyConfig/         # Agent configurations
├── 📦 Generated Projects
│   └── WareHouse/             # Output directory
└── 📚 Documentation
    ├── README.md              # Updated with web instructions
    ├── wiki.md                # Preserved original docs
    └── DEPLOYMENT_GUIDE.md    # This file
```

## 🚀 Quick Deployment

### **1. Prerequisites**
```bash
# Python 3.8+
python --version

# OpenAI API Key
export OPENAI_API_KEY="your-key-here"
```

### **2. Installation**
```bash
# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "import flask, flask_socketio; print('✅ Dependencies OK')"
```

### **3. Launch**
```bash
# Start web application
python start_web.py

# Custom configuration
python start_web.py --host 0.0.0.0 --port 8080 --debug
```

### **4. Access**
- **Dashboard**: http://localhost:5000
- **API Docs**: http://localhost:5000/api/docs
- **Legacy Visualizer**: http://localhost:5000/visualizer

## 🏢 2D Office Interface Features

### **Virtual Workstations**
- **CEO Desk** 👔 - Strategic oversight
- **CTO Station** 💻 - Technical decisions  
- **CPO Area** 📊 - Product management
- **Developer Zone** 👨‍💻 - Code implementation
- **Review Corner** 🔍 - Quality assurance
- **Test Lab** 🧪 - Validation testing
- **Design Studio** 🎨 - UI/UX creation

### **Real-time Indicators**
- **Agent Status** - Idle/Working animations
- **Progress Bars** - Development phase tracking
- **Activity Lights** - Work indicators
- **Task Assignments** - Current responsibilities

### **Interactive Elements**
- **Click agents** to see current tasks
- **Hover workstations** for details
- **Real-time updates** via WebSocket
- **Floating particles** for ambiance

## 🔌 API Endpoints

### **Task Management**
```http
POST /api/tasks              # Create new project
GET  /api/tasks              # List all projects
GET  /api/tasks/{id}         # Get project details
GET  /api/tasks/{id}/download # Download generated software
```

### **Agent Monitoring**
```http
GET  /api/agents/status      # Current agent activities
GET  /api/configs            # Available configurations
```

### **WebSocket Events**
```javascript
// Real-time updates
socket.on('task_created', callback)
socket.on('task_updated', callback)
socket.on('phase_update', callback)
socket.on('agent_status_update', callback)
```

## 🔧 Configuration Options

### **Development Modes**
- **Default** - Standard workflow
- **Art** - With UI/UX design
- **Human** - Human-AI collaboration
- **Incremental** - Build on existing code

### **Environment Variables**
```bash
OPENAI_API_KEY=your-key-here
FLASK_ENV=development
FLASK_DEBUG=1
```

## 📊 Monitoring & Analytics

### **Task Tracking**
- Real-time progress updates
- Phase-by-phase monitoring
- Error detection and reporting
- Completion notifications

### **Agent Performance**
- Work distribution visualization
- Idle/active time tracking
- Task assignment history
- Collaboration patterns

## 🔒 Security Considerations

### **Local Development**
- No authentication required
- Localhost access only
- API key via environment

### **Production Deployment**
- Add authentication middleware
- Configure HTTPS
- Implement rate limiting
- Set up monitoring

## 🐳 Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "start_web.py", "--host", "0.0.0.0"]
```

```bash
# Build and run
docker build -t chatdev-web .
docker run -p 5000:5000 -e OPENAI_API_KEY=your-key chatdev-web
```

## 🌐 Cloud Deployment

### **Heroku**
```bash
# Create Procfile
echo "web: python start_web.py --host 0.0.0.0 --port \$PORT" > Procfile

# Deploy
heroku create your-chatdev-app
heroku config:set OPENAI_API_KEY=your-key
git push heroku main
```

### **AWS/GCP/Azure**
- Use container services (ECS, Cloud Run, Container Instances)
- Set environment variables in cloud console
- Configure load balancer for scaling

## 🔄 Migration from CLI

### **Existing Projects**
- Old projects remain in `example_project/`
- New projects go to `WareHouse/`
- Configurations preserved in `CompanyConfig/`

### **Backward Compatibility**
- Original `run.py` still works
- All CLI features preserved
- Web interface is additive

## 🛠️ Customization

### **Adding New Agent Types**
1. Update `CompanyConfig/Default/RoleConfig.json`
2. Add workstation in `dashboard.html`
3. Style in `dashboard.css`
4. Handle in `dashboard.js`

### **Custom Workflows**
1. Create new config in `CompanyConfig/`
2. Define phases in `ChatChainConfig.json`
3. Add to web interface dropdown

### **UI Themes**
- Modify `static/css/dashboard.css`
- Add new color schemes
- Customize agent avatars
- Change office layout

## 📈 Performance Optimization

### **Frontend**
- Lazy load components
- Optimize WebSocket events
- Cache static assets
- Minimize DOM updates

### **Backend**
- Use Redis for task queue
- Implement connection pooling
- Add response caching
- Monitor memory usage

## 🐛 Troubleshooting

### **Common Issues**
```bash
# Port already in use
python start_web.py --port 8080

# Missing dependencies
pip install -r requirements.txt

# OpenAI API errors
export OPENAI_API_KEY=your-key

# WebSocket connection issues
# Check firewall settings
```

### **Debug Mode**
```bash
python start_web.py --debug
```

## 📚 Additional Resources

- **Original Documentation**: `wiki.md`
- **API Reference**: http://localhost:5000/api/docs
- **Example Project**: `example_project/`
- **Configuration Guide**: `CompanyConfig/`

## 🎉 Success Metrics

After deployment, you should see:
- ✅ Web dashboard loads successfully
- ✅ AI agents visible in 2D office
- ✅ Tasks can be created via web form
- ✅ Real-time updates work
- ✅ Generated software downloads
- ✅ API endpoints respond correctly

## 🚀 Next Steps

1. **Create your first web project**
2. **Explore the 2D office interface**
3. **Try different development modes**
4. **Monitor agent collaboration**
5. **Download and test generated software**

Welcome to the future of AI-powered software development! 🎊
