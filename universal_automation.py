#!/usr/bin/env python3
"""
Universal Task Automation Framework
Extends ChatDev beyond software development to handle any type of task automation.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class TaskCategory(Enum):
    """Categories of tasks that can be automated"""
    SOFTWARE_DEVELOPMENT = "software_development"
    BUSINESS_OPERATIONS = "business_operations"
    CONTENT_CREATION = "content_creation"
    RESEARCH_ANALYSIS = "research_analysis"
    PROJECT_MANAGEMENT = "project_management"
    CREATIVE_PROJECTS = "creative_projects"
    PERSONAL_PRODUCTIVITY = "personal_productivity"
    EDUCATION_TRAINING = "education_training"
    MARKETING_SALES = "marketing_sales"
    CUSTOMER_SERVICE = "customer_service"
    FINANCIAL_PLANNING = "financial_planning"
    EVENT_PLANNING = "event_planning"
    CUSTOM = "custom"

class MBTIType(Enum):
    """MBTI personality types for AI workers"""
    INTJ = "INTJ"  # Architect
    INTP = "INTP"  # Thinker
    ENTJ = "ENTJ"  # Commander
    ENTP = "ENTP"  # Debater
    INFJ = "INFJ"  # Advocate
    INFP = "INFP"  # Mediator
    ENFJ = "ENFJ"  # Protagonist
    ENFP = "ENFP"  # Campaigner
    ISTJ = "ISTJ"  # Logistician
    ISFJ = "ISFJ"  # Protector
    ESTJ = "ESTJ"  # Executive
    ESFJ = "ESFJ"  # Consul
    ISTP = "ISTP"  # Virtuoso
    ISFP = "ISFP"  # Adventurer
    ESTP = "ESTP"  # Entrepreneur
    ESFP = "ESFP"  # Entertainer

@dataclass
class WorkerPersonality:
    """Defines personality traits and behavior patterns for AI workers"""
    mbti_type: MBTIType
    communication_style: str  # formal, casual, enthusiastic, analytical, etc.
    decision_making: str      # quick, deliberate, collaborative, independent
    work_approach: str        # methodical, creative, efficient, thorough
    stress_response: str      # calm, energetic, focused, adaptive
    collaboration_preference: str  # leader, supporter, independent, facilitator
    
    def get_personality_prompt(self) -> str:
        """Generate personality-specific prompt additions"""
        mbti_traits = {
            MBTIType.INTJ: "You are strategic, independent, and focus on long-term vision. You prefer working alone and value competence.",
            MBTIType.ENFP: "You are enthusiastic, creative, and people-focused. You generate many ideas and inspire others.",
            MBTIType.ESTJ: "You are organized, practical, and results-oriented. You like structure and clear processes.",
            MBTIType.INFP: "You are idealistic, creative, and value-driven. You work best when aligned with your values.",
            # Add more MBTI-specific traits...
        }
        
        base_trait = mbti_traits.get(self.mbti_type, "You are professional and dedicated to your work.")
        
        return f"{base_trait} Your communication style is {self.communication_style}. " \
               f"You approach work in a {self.work_approach} manner and prefer {self.collaboration_preference} collaboration."

@dataclass
class CustomWorker:
    """Represents a custom AI worker in the user's office"""
    id: str
    name: str
    role: str
    department: str
    skills: List[str]
    personality: WorkerPersonality
    avatar_emoji: str
    experience_level: str  # junior, mid, senior, expert
    specializations: List[str]
    created_at: str
    is_active: bool = True
    
    @classmethod
    def create_default_workers(cls) -> List['CustomWorker']:
        """Create a set of default workers for new users"""
        return [
            cls(
                id=str(uuid.uuid4()),
                name="Alex Chen",
                role="Strategic Planner",
                department="Leadership",
                skills=["strategic_thinking", "project_planning", "decision_making"],
                personality=WorkerPersonality(
                    mbti_type=MBTIType.ENTJ,
                    communication_style="direct and confident",
                    decision_making="quick and decisive",
                    work_approach="strategic and goal-oriented",
                    stress_response="focused and determined",
                    collaboration_preference="leader"
                ),
                avatar_emoji="👔",
                experience_level="expert",
                specializations=["business_strategy", "team_leadership"],
                created_at=datetime.now().isoformat()
            ),
            cls(
                id=str(uuid.uuid4()),
                name="Maya Rodriguez",
                role="Creative Director",
                department="Creative",
                skills=["creative_thinking", "design", "content_creation"],
                personality=WorkerPersonality(
                    mbti_type=MBTIType.ENFP,
                    communication_style="enthusiastic and inspiring",
                    decision_making="collaborative and intuitive",
                    work_approach="creative and flexible",
                    stress_response="energetic and adaptive",
                    collaboration_preference="facilitator"
                ),
                avatar_emoji="🎨",
                experience_level="senior",
                specializations=["visual_design", "content_strategy"],
                created_at=datetime.now().isoformat()
            ),
            cls(
                id=str(uuid.uuid4()),
                name="Dr. Sam Kim",
                role="Research Analyst",
                department="Research",
                skills=["research", "data_analysis", "critical_thinking"],
                personality=WorkerPersonality(
                    mbti_type=MBTIType.INTJ,
                    communication_style="analytical and precise",
                    decision_making="deliberate and evidence-based",
                    work_approach="methodical and thorough",
                    stress_response="calm and systematic",
                    collaboration_preference="independent"
                ),
                avatar_emoji="🔬",
                experience_level="expert",
                specializations=["market_research", "data_science"],
                created_at=datetime.now().isoformat()
            )
        ]

@dataclass
class TaskTemplate:
    """Template for common automation scenarios"""
    id: str
    name: str
    category: TaskCategory
    description: str
    required_roles: List[str]
    estimated_duration: str
    complexity: str  # simple, moderate, complex
    steps: List[Dict[str, Any]]
    example_inputs: Dict[str, Any]
    example_outputs: Dict[str, Any]
    
    @classmethod
    def get_default_templates(cls) -> List['TaskTemplate']:
        """Get pre-built task templates"""
        return [
            cls(
                id="business_plan_creation",
                name="Business Plan Creation",
                category=TaskCategory.BUSINESS_OPERATIONS,
                description="Create a comprehensive business plan with market analysis, financial projections, and strategy",
                required_roles=["Strategic Planner", "Research Analyst", "Financial Analyst"],
                estimated_duration="2-4 hours",
                complexity="complex",
                steps=[
                    {"phase": "market_research", "role": "Research Analyst", "description": "Analyze target market and competition"},
                    {"phase": "strategy_development", "role": "Strategic Planner", "description": "Develop business strategy and goals"},
                    {"phase": "financial_planning", "role": "Financial Analyst", "description": "Create financial projections and budgets"},
                    {"phase": "document_creation", "role": "Content Creator", "description": "Compile comprehensive business plan document"}
                ],
                example_inputs={"business_idea": "AI-powered fitness app", "target_market": "health-conscious millennials"},
                example_outputs={"business_plan": "comprehensive_plan.pdf", "financial_model": "projections.xlsx"}
            ),
            cls(
                id="content_marketing_campaign",
                name="Content Marketing Campaign",
                category=TaskCategory.MARKETING_SALES,
                description="Design and create a multi-channel content marketing campaign",
                required_roles=["Creative Director", "Content Creator", "Marketing Strategist"],
                estimated_duration="1-2 hours",
                complexity="moderate",
                steps=[
                    {"phase": "strategy_planning", "role": "Marketing Strategist", "description": "Define campaign goals and target audience"},
                    {"phase": "content_ideation", "role": "Creative Director", "description": "Brainstorm content ideas and themes"},
                    {"phase": "content_creation", "role": "Content Creator", "description": "Create blog posts, social media content, and visuals"},
                    {"phase": "campaign_scheduling", "role": "Marketing Strategist", "description": "Plan content calendar and distribution"}
                ],
                example_inputs={"product": "eco-friendly water bottles", "campaign_duration": "3 months"},
                example_outputs={"content_calendar": "calendar.pdf", "social_posts": "posts/", "blog_articles": "articles/"}
            ),
            cls(
                id="personal_productivity_system",
                name="Personal Productivity System",
                category=TaskCategory.PERSONAL_PRODUCTIVITY,
                description="Design a personalized productivity system with tools and workflows",
                required_roles=["Productivity Coach", "Systems Analyst"],
                estimated_duration="30-60 minutes",
                complexity="simple",
                steps=[
                    {"phase": "assessment", "role": "Productivity Coach", "description": "Assess current productivity challenges"},
                    {"phase": "system_design", "role": "Systems Analyst", "description": "Design custom productivity workflow"},
                    {"phase": "tool_recommendations", "role": "Productivity Coach", "description": "Recommend specific tools and apps"},
                    {"phase": "implementation_guide", "role": "Productivity Coach", "description": "Create step-by-step implementation guide"}
                ],
                example_inputs={"work_type": "remote software developer", "main_challenges": "time management, focus"},
                example_outputs={"productivity_system": "system_guide.md", "tool_list": "recommended_tools.json"}
            )
        ]

class UniversalTaskAutomation:
    """Main class for handling universal task automation"""
    
    def __init__(self):
        self.workers: Dict[str, CustomWorker] = {}
        self.templates: Dict[str, TaskTemplate] = {}
        self.active_tasks: Dict[str, Dict] = {}
        self.office_config: Dict = {}
        
        # Load default templates
        for template in TaskTemplate.get_default_templates():
            self.templates[template.id] = template
    
    def create_custom_worker(self, name: str, role: str, department: str, 
                           skills: List[str], mbti_type: MBTIType, 
                           avatar_emoji: str = "👤") -> CustomWorker:
        """Create a new custom worker"""
        personality = WorkerPersonality(
            mbti_type=mbti_type,
            communication_style="professional",
            decision_making="collaborative",
            work_approach="efficient",
            stress_response="calm",
            collaboration_preference="supporter"
        )
        
        worker = CustomWorker(
            id=str(uuid.uuid4()),
            name=name,
            role=role,
            department=department,
            skills=skills,
            personality=personality,
            avatar_emoji=avatar_emoji,
            experience_level="mid",
            specializations=[],
            created_at=datetime.now().isoformat()
        )
        
        self.workers[worker.id] = worker
        return worker
    
    def get_suitable_workers(self, required_roles: List[str]) -> List[CustomWorker]:
        """Find workers suitable for specific roles"""
        suitable_workers = []
        for worker in self.workers.values():
            if worker.is_active and (worker.role in required_roles or 
                                   any(skill in required_roles for skill in worker.skills)):
                suitable_workers.append(worker)
        return suitable_workers
    
    def create_task_from_template(self, template_id: str, inputs: Dict[str, Any]) -> Dict:
        """Create a new task based on a template"""
        if template_id not in self.templates:
            raise ValueError(f"Template {template_id} not found")
        
        template = self.templates[template_id]
        task_id = str(uuid.uuid4())
        
        # Find suitable workers for this task
        suitable_workers = self.get_suitable_workers(template.required_roles)
        
        task = {
            "id": task_id,
            "template_id": template_id,
            "name": template.name,
            "category": template.category.value,
            "description": template.description,
            "inputs": inputs,
            "assigned_workers": [worker.id for worker in suitable_workers[:len(template.required_roles)]],
            "status": "created",
            "created_at": datetime.now().isoformat(),
            "steps": template.steps,
            "current_step": 0,
            "progress": 0
        }
        
        self.active_tasks[task_id] = task
        return task
    
    def get_worker_prompt(self, worker_id: str, task_context: str) -> str:
        """Generate a personality-aware prompt for a specific worker"""
        if worker_id not in self.workers:
            return task_context
        
        worker = self.workers[worker_id]
        personality_prompt = worker.personality.get_personality_prompt()
        
        return f"""You are {worker.name}, a {worker.role} in the {worker.department} department.
{personality_prompt}

Your skills include: {', '.join(worker.skills)}
Your specializations: {', '.join(worker.specializations)}

Task context: {task_context}

Please approach this task according to your personality and expertise."""

# Export the main classes for use in the web application
__all__ = ['UniversalTaskAutomation', 'CustomWorker', 'TaskTemplate', 'TaskCategory', 'MBTIType', 'WorkerPersonality']
