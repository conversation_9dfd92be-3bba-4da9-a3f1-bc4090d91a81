{"chain": [{"phase": "DemandAnalysis", "phaseType": "SimplePhase", "max_turn_step": 2, "need_reflect": "True"}, {"phase": "LanguageChoose", "phaseType": "SimplePhase", "max_turn_step": 2, "need_reflect": "True"}, {"phase": "Coding", "phaseType": "SimplePhase", "max_turn_step": 1, "need_reflect": "False"}, {"phase": "CodeCompleteAll", "phaseType": "ComposedPhase", "cycleNum": 20, "Composition": [{"phase": "CodeComplete", "phaseType": "SimplePhase", "max_turn_step": 1, "need_reflect": "False"}]}, {"phase": "CodeReview", "phaseType": "ComposedPhase", "cycleNum": 5, "Composition": [{"phase": "CodeReviewComment", "phaseType": "SimplePhase", "max_turn_step": -1, "need_reflect": "False"}, {"phase": "CodeReviewModification", "phaseType": "SimplePhase", "max_turn_step": -1, "need_reflect": "False"}]}, {"phase": "Test", "phaseType": "ComposedPhase", "cycleNum": 5, "Composition": [{"phase": "TestError<PERSON><PERSON><PERSON><PERSON>", "phaseType": "SimplePhase", "max_turn_step": -1, "need_reflect": "False"}, {"phase": "TestModification", "phaseType": "SimplePhase", "max_turn_step": -1, "need_reflect": "False"}]}, {"phase": "EnvironmentDoc", "phaseType": "SimplePhase", "max_turn_step": 1, "need_reflect": "True"}, {"phase": "Manual", "phaseType": "SimplePhase", "max_turn_step": 1, "need_reflect": "False"}], "recruitments": ["Chief Executive Officer", "Counselor", "Chief Human Resource Officer", "Chief Product Officer", "Chief Technology Officer", "Programmer", "Code Reviewer", "Software Test Engineer", "Chief Creative Officer"], "clear_structure": "True", "brainstorming": "False", "gui_design": "True", "git_management": "False", "self_improve": "True"}