<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatDev - AI Software Development Office</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <h1>🏢 Your AI Company</h1>
                <p>Universal Task Automation Platform</p>
            </div>
            <div class="header-controls">
                <a href="/office" class="office-management-link">⚙️ Manage Office & Workers</a>
                <div class="status-indicators">
                    <div class="indicator" id="connection-status">
                        <span class="dot offline"></span>
                        <span>Disconnected</span>
                    </div>
                    <div class="indicator" id="active-tasks">
                        <span class="count">0</span>
                        <span>Active Tasks</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel: 2D Office View -->
            <div class="office-panel">
                <h2>🏢 Virtual Office</h2>
                <div class="office-container">
                    <div class="office-grid" id="office-grid">
                        <!-- Agent workstations will be dynamically generated -->
                        <div class="workstation ceo" data-agent="CEO">
                            <div class="agent-avatar">👔</div>
                            <div class="agent-info">
                                <div class="agent-name">CEO</div>
                                <div class="agent-status" id="ceo-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="ceo-indicator"></div>
                        </div>

                        <div class="workstation cto" data-agent="CTO">
                            <div class="agent-avatar">💻</div>
                            <div class="agent-info">
                                <div class="agent-name">CTO</div>
                                <div class="agent-status" id="cto-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="cto-indicator"></div>
                        </div>

                        <div class="workstation cpo" data-agent="CPO">
                            <div class="agent-avatar">📊</div>
                            <div class="agent-info">
                                <div class="agent-name">CPO</div>
                                <div class="agent-status" id="cpo-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="cpo-indicator"></div>
                        </div>

                        <div class="workstation programmer" data-agent="Programmer">
                            <div class="agent-avatar">👨‍💻</div>
                            <div class="agent-info">
                                <div class="agent-name">Programmer</div>
                                <div class="agent-status" id="programmer-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="programmer-indicator"></div>
                        </div>

                        <div class="workstation reviewer" data-agent="Code Reviewer">
                            <div class="agent-avatar">🔍</div>
                            <div class="agent-info">
                                <div class="agent-name">Reviewer</div>
                                <div class="agent-status" id="reviewer-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="reviewer-indicator"></div>
                        </div>

                        <div class="workstation tester" data-agent="Tester">
                            <div class="agent-avatar">🧪</div>
                            <div class="agent-info">
                                <div class="agent-name">Tester</div>
                                <div class="agent-status" id="tester-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="tester-indicator"></div>
                        </div>

                        <div class="workstation designer" data-agent="Designer">
                            <div class="agent-avatar">🎨</div>
                            <div class="agent-info">
                                <div class="agent-name">Designer</div>
                                <div class="agent-status" id="designer-status">Idle</div>
                            </div>
                            <div class="work-indicator" id="designer-indicator"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Task Management -->
            <div class="control-panel">
                <!-- New Task Form -->
                <div class="task-form-section">
                    <h2>🚀 Automate Any Task</h2>
                    <form id="new-task-form" class="task-form">
                        <div class="form-group">
                            <label for="task-type">Task Type:</label>
                            <select id="task-type" name="task_type">
                                <option value="custom">🎯 Custom Task</option>
                                <option value="software_development">💻 Software Development</option>
                                <option value="business_operations">📊 Business Operations</option>
                                <option value="content_creation">📝 Content Creation</option>
                                <option value="research_analysis">🔬 Research & Analysis</option>
                                <option value="marketing_sales">📈 Marketing & Sales</option>
                                <option value="personal_productivity">⚡ Personal Productivity</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="project-name">Task/Project Name:</label>
                            <input type="text" id="project-name" name="project_name" required
                                   placeholder="e.g., Business Plan, Marketing Campaign, Research Report">
                        </div>

                        <div class="form-group">
                            <label for="task-description">Task Description:</label>
                            <textarea id="task-description" name="description" required
                                      placeholder="Describe what you want to accomplish..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="config-type">Automation Mode:</label>
                            <select id="config-type" name="config">
                                <option value="Default">Standard Automation</option>
                                <option value="Art">Creative & Design Focus</option>
                                <option value="Human">Human-AI Collaboration</option>
                                <option value="Incremental">Build on Existing Work</option>
                            </select>
                        </div>

                        <button type="submit" class="submit-btn">
                            <span class="btn-text">🚀 Start Automation</span>
                            <span class="btn-loading" style="display: none;">Creating...</span>
                        </button>
                    </form>
                </div>

                <!-- Active Tasks -->
                <div class="tasks-section">
                    <h2>📋 Active Projects</h2>
                    <div id="tasks-container" class="tasks-container">
                        <div class="no-tasks" id="no-tasks">
                            <p>No active projects. Create one above!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Modal -->
        <div id="progress-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">Project Development Progress</h3>
                    <span class="close" id="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="progress-info">
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <div class="progress-text" id="progress-text">0%</div>
                        </div>
                        <div class="current-phase" id="current-phase">Initializing...</div>
                    </div>
                    <div class="phase-timeline" id="phase-timeline">
                        <!-- Phase indicators will be added dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
