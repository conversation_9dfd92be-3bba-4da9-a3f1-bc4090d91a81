<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Office Management - Your AI Company</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/office_management.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <h1>🏢 Your AI Company</h1>
                <p>Manage Your Virtual Office & Workers</p>
            </div>
            <nav class="nav-tabs">
                <button class="tab-btn active" data-tab="workers">👥 Workers</button>
                <button class="tab-btn" data-tab="office">🏢 Office Design</button>
                <button class="tab-btn" data-tab="tasks">📋 Task Templates</button>
                <button class="tab-btn" data-tab="automation">⚡ Automation</button>
            </nav>
        </header>

        <!-- Workers Management Tab -->
        <div class="tab-content active" id="workers-tab">
            <div class="section-header">
                <h2>👥 Your AI Workers</h2>
                <button class="btn-primary" id="add-worker-btn">+ Hire New Worker</button>
            </div>

            <div class="workers-grid" id="workers-grid">
                <!-- Workers will be dynamically loaded here -->
            </div>

            <!-- Add Worker Modal -->
            <div id="add-worker-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>🎯 Hire New AI Worker</h3>
                        <span class="close" id="close-worker-modal">&times;</span>
                    </div>
                    <form id="add-worker-form" class="worker-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="worker-name">Name:</label>
                                <input type="text" id="worker-name" required placeholder="e.g., Sarah Johnson">
                            </div>
                            <div class="form-group">
                                <label for="worker-avatar">Avatar:</label>
                                <select id="worker-avatar" required>
                                    <option value="👔">👔 Executive</option>
                                    <option value="💻">💻 Tech Expert</option>
                                    <option value="🎨">🎨 Creative</option>
                                    <option value="📊">📊 Analyst</option>
                                    <option value="🔬">🔬 Researcher</option>
                                    <option value="📝">📝 Writer</option>
                                    <option value="💡">💡 Strategist</option>
                                    <option value="🎯">🎯 Manager</option>
                                    <option value="🚀">🚀 Innovator</option>
                                    <option value="🤝">🤝 Coordinator</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="worker-role">Role/Job Title:</label>
                                <input type="text" id="worker-role" required placeholder="e.g., Marketing Director">
                            </div>
                            <div class="form-group">
                                <label for="worker-department">Department:</label>
                                <select id="worker-department" required>
                                    <option value="Leadership">Leadership</option>
                                    <option value="Creative">Creative</option>
                                    <option value="Technology">Technology</option>
                                    <option value="Research">Research</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Operations">Operations</option>
                                    <option value="Finance">Finance</option>
                                    <option value="Human Resources">Human Resources</option>
                                    <option value="Customer Service">Customer Service</option>
                                    <option value="Sales">Sales</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="worker-mbti">MBTI Personality Type:</label>
                            <select id="worker-mbti" required>
                                <option value="INTJ">INTJ - The Architect (Strategic, Independent)</option>
                                <option value="INTP">INTP - The Thinker (Analytical, Creative)</option>
                                <option value="ENTJ">ENTJ - The Commander (Leadership, Decisive)</option>
                                <option value="ENTP">ENTP - The Debater (Innovative, Energetic)</option>
                                <option value="INFJ">INFJ - The Advocate (Insightful, Principled)</option>
                                <option value="INFP">INFP - The Mediator (Idealistic, Loyal)</option>
                                <option value="ENFJ">ENFJ - The Protagonist (Charismatic, Inspiring)</option>
                                <option value="ENFP">ENFP - The Campaigner (Enthusiastic, Creative)</option>
                                <option value="ISTJ">ISTJ - The Logistician (Practical, Reliable)</option>
                                <option value="ISFJ">ISFJ - The Protector (Warm, Responsible)</option>
                                <option value="ESTJ">ESTJ - The Executive (Organized, Traditional)</option>
                                <option value="ESFJ">ESFJ - The Consul (Caring, Social)</option>
                                <option value="ISTP">ISTP - The Virtuoso (Bold, Practical)</option>
                                <option value="ISFP">ISFP - The Adventurer (Artistic, Charming)</option>
                                <option value="ESTP">ESTP - The Entrepreneur (Energetic, Perceptive)</option>
                                <option value="ESFP">ESFP - The Entertainer (Spontaneous, Enthusiastic)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="worker-skills">Skills (comma-separated):</label>
                            <input type="text" id="worker-skills" required 
                                   placeholder="e.g., strategic planning, data analysis, creative writing">
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="worker-experience">Experience Level:</label>
                                <select id="worker-experience" required>
                                    <option value="junior">Junior (0-2 years)</option>
                                    <option value="mid">Mid-level (3-5 years)</option>
                                    <option value="senior">Senior (6-10 years)</option>
                                    <option value="expert">Expert (10+ years)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="worker-communication">Communication Style:</label>
                                <select id="worker-communication" required>
                                    <option value="direct and confident">Direct & Confident</option>
                                    <option value="warm and supportive">Warm & Supportive</option>
                                    <option value="analytical and precise">Analytical & Precise</option>
                                    <option value="enthusiastic and inspiring">Enthusiastic & Inspiring</option>
                                    <option value="calm and methodical">Calm & Methodical</option>
                                    <option value="creative and expressive">Creative & Expressive</option>
                                </select>
                            </div>
                        </div>

                        <button type="submit" class="btn-primary">🎉 Hire Worker</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Office Design Tab -->
        <div class="tab-content" id="office-tab">
            <div class="section-header">
                <h2>🏢 Design Your Office</h2>
                <div class="office-controls">
                    <select id="office-theme">
                        <option value="modern">🏢 Modern</option>
                        <option value="cozy">🏠 Cozy</option>
                        <option value="creative">🎨 Creative</option>
                        <option value="nature">🌿 Nature</option>
                        <option value="minimalist">⚪ Minimalist</option>
                        <option value="corporate">🏛️ Corporate</option>
                    </select>
                    <button class="btn-secondary" id="save-office-btn">💾 Save Layout</button>
                </div>
            </div>

            <div class="office-designer">
                <div class="workstation-palette">
                    <h3>🛠️ Add Workstations</h3>
                    <div class="palette-items">
                        <div class="palette-item" data-type="desk">
                            <div class="item-icon">🖥️</div>
                            <div class="item-name">Desk</div>
                        </div>
                        <div class="palette-item" data-type="executive_office">
                            <div class="item-icon">👔</div>
                            <div class="item-name">Executive Office</div>
                        </div>
                        <div class="palette-item" data-type="creative_space">
                            <div class="item-icon">🎨</div>
                            <div class="item-name">Creative Space</div>
                        </div>
                        <div class="palette-item" data-type="meeting_room">
                            <div class="item-icon">🤝</div>
                            <div class="item-name">Meeting Room</div>
                        </div>
                        <div class="palette-item" data-type="research_lab">
                            <div class="item-icon">🔬</div>
                            <div class="item-name">Research Lab</div>
                        </div>
                        <div class="palette-item" data-type="break_room">
                            <div class="item-icon">☕</div>
                            <div class="item-name">Break Room</div>
                        </div>
                    </div>
                </div>

                <div class="office-canvas" id="office-canvas">
                    <div class="canvas-grid" id="canvas-grid">
                        <!-- Office grid will be generated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Templates Tab -->
        <div class="tab-content" id="tasks-tab">
            <div class="section-header">
                <h2>📋 Task Templates</h2>
                <button class="btn-primary" id="create-template-btn">+ Create Template</button>
            </div>

            <div class="templates-grid" id="templates-grid">
                <!-- Task templates will be loaded here -->
            </div>
        </div>

        <!-- Automation Tab -->
        <div class="tab-content" id="automation-tab">
            <div class="section-header">
                <h2>⚡ Task Automation</h2>
                <button class="btn-primary" id="start-automation-btn">🚀 Start New Task</button>
            </div>

            <div class="automation-dashboard">
                <div class="active-tasks" id="active-tasks">
                    <h3>🔄 Active Automations</h3>
                    <!-- Active tasks will be shown here -->
                </div>

                <div class="task-history" id="task-history">
                    <h3>📚 Completed Tasks</h3>
                    <!-- Completed tasks will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <script src="{{ url_for('static', filename='js/office_management.js') }}"></script>
</body>
</html>
