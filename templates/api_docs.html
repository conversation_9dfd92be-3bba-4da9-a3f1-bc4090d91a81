<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatDev API Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 { color: #333; }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .method.get { background: #28a745; }
        .method.post { background: #007bff; }
        .method.put { background: #ffc107; color: #333; }
        .method.delete { background: #dc3545; }
        code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .nav {
            background: #007bff;
            color: white;
            padding: 15px;
            margin: -30px -30px 30px -30px;
            border-radius: 10px 10px 0 0;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <h1>ChatDev API Documentation</h1>
            <a href="/">← Back to Dashboard</a>
            <a href="/visualizer">Visualizer</a>
        </div>

        <h2>Overview</h2>
        <p>The ChatDev API provides endpoints for managing AI-powered software development tasks. You can create projects, monitor progress, and download generated software.</p>

        <h2>Base URL</h2>
        <code>http://localhost:5000/api</code>

        <h2>Authentication</h2>
        <p>Currently, no authentication is required for local development. Ensure your OpenAI API key is configured via environment variables.</p>

        <h2>Endpoints</h2>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /api/tasks</h3>
            <p><strong>Create a new development task</strong></p>
            
            <h4>Request Body:</h4>
            <pre><code>{
  "project_name": "string (required)",
  "description": "string (required)", 
  "config": "string (optional, default: 'Default')"
}</code></pre>

            <h4>Config Options:</h4>
            <ul>
                <li><code>Default</code> - Standard development workflow</li>
                <li><code>Art</code> - Includes UI/UX design capabilities</li>
                <li><code>Human</code> - Human-AI collaborative development</li>
                <li><code>Incremental</code> - Build upon existing codebases</li>
            </ul>

            <h4>Response:</h4>
            <pre><code>{
  "task_id": "uuid",
  "status": "created"
}</code></pre>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/tasks</h3>
            <p><strong>Get all tasks</strong></p>
            
            <h4>Response:</h4>
            <pre><code>[
  {
    "id": "uuid",
    "description": "string",
    "project_name": "string",
    "config": "string",
    "status": "queued|running|completed|failed",
    "created_at": "ISO datetime",
    "started_at": "ISO datetime",
    "completed_at": "ISO datetime",
    "progress": 0-100,
    "current_phase": "string",
    "log_file": "string",
    "output_directory": "string",
    "error": "string"
  }
]</code></pre>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/tasks/{task_id}</h3>
            <p><strong>Get specific task details</strong></p>
            
            <h4>Response:</h4>
            <pre><code>{
  "id": "uuid",
  "description": "string",
  "project_name": "string",
  "status": "string",
  "progress": 0-100,
  "current_phase": "string",
  ...
}</code></pre>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/tasks/{task_id}/download</h3>
            <p><strong>Download generated software</strong></p>
            <p>Returns a ZIP file containing all generated project files.</p>
            
            <h4>Requirements:</h4>
            <ul>
                <li>Task must have status <code>completed</code></li>
                <li>Output directory must exist</li>
            </ul>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/agents/status</h3>
            <p><strong>Get current status of all AI agents</strong></p>
            
            <h4>Response:</h4>
            <pre><code>{
  "CEO": {
    "status": "idle|working",
    "current_task": "task_id or null"
  },
  "CTO": {
    "status": "idle|working", 
    "current_task": "task_id or null"
  },
  ...
}</code></pre>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/configs</h3>
            <p><strong>Get available configuration options</strong></p>
            
            <h4>Response:</h4>
            <pre><code>["Default", "Art", "Human", "Incremental"]</code></pre>
        </div>

        <h2>WebSocket Events</h2>
        <p>Real-time updates are provided via WebSocket connection to <code>/</code></p>

        <h3>Client Events (Emit)</h3>
        <ul>
            <li><code>connect</code> - Establish connection</li>
            <li><code>request_task_updates</code> - Request current task status</li>
        </ul>

        <h3>Server Events (Listen)</h3>
        <ul>
            <li><code>connected</code> - Connection established</li>
            <li><code>task_created</code> - New task created</li>
            <li><code>task_updated</code> - Task status changed</li>
            <li><code>phase_update</code> - Development phase changed</li>
            <li><code>agent_status_update</code> - Agent status changed</li>
            <li><code>agents_status</code> - All agent statuses</li>
        </ul>

        <h2>Development Phases</h2>
        <p>Tasks progress through the following phases:</p>
        <ol>
            <li><strong>Preprocessing</strong> - Initial setup</li>
            <li><strong>Recruitment</strong> - Agent assignment</li>
            <li><strong>DemandAnalysis</strong> - Requirements analysis</li>
            <li><strong>LanguageChoose</strong> - Technology selection</li>
            <li><strong>Coding</strong> - Code generation</li>
            <li><strong>CodeReview</strong> - Quality assurance</li>
            <li><strong>Test</strong> - Testing and validation</li>
            <li><strong>Manual</strong> - Documentation generation</li>
            <li><strong>Completed</strong> - Project finished</li>
        </ol>

        <h2>Error Handling</h2>
        <p>API errors return appropriate HTTP status codes with JSON error messages:</p>
        <pre><code>{
  "error": "Error description"
}</code></pre>

        <h2>Example Usage</h2>
        <h3>Create a Task (JavaScript)</h3>
        <pre><code>const response = await fetch('/api/tasks', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    project_name: 'TodoApp',
    description: 'Create a simple todo list application with add, delete, and mark complete features',
    config: 'Default'
  })
});

const result = await response.json();
console.log('Task created:', result.task_id);</code></pre>

        <h3>Monitor Progress (WebSocket)</h3>
        <pre><code>const socket = io();

socket.on('task_updated', (task) => {
  console.log(`Task ${task.project_name}: ${task.progress}% - ${task.current_phase}`);
});

socket.on('agent_status_update', (data) => {
  console.log(`Agent ${data.agent} is now ${data.status}`);
});</code></pre>
    </div>
</body>
</html>
