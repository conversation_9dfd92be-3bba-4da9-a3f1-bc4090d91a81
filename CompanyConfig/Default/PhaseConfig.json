{"DemandAnalysis": {"assistant_role_name": "Chief Product Officer", "user_role_name": "Chief Executive Officer", "phase_prompt": ["ChatDev has made products in the following form before:", "Image: can present information in line chart, bar chart, flow chart, cloud chart, Gantt chart, etc.", "Document: can present information via .docx files.", "PowerPoint: can present information via .pptx files.", "Excel: can present information via .xlsx files.", "PDF: can present information via .pdf files.", "Website: can present personal resume, tutorial, products, or ideas, via .html files.", "Application: can implement visualized game, software, tool, etc, via python.", "Dashboard: can display a panel visualizing real-time information.", "Mind Map: can represent ideas, with related concepts arranged around a core concept.", "As the {assistant_role}, to satisfy the new user's demand and the product should be realizable, you should keep discussing with me to decide which product modality do we want the product to be?", "Note that we must ONLY discuss the product modality and do not discuss anything else! Once we all have expressed our opinion(s) and agree with the results of the discussion unanimously, any of us must actively terminate the discussion by replying with only one line, which starts with a single word <INFO>, followed by our final product modality without any other words, e.g., \"<INFO> PowerPoint\"."]}, "LanguageChoose": {"assistant_role_name": "Chief Technology Officer", "user_role_name": "Chief Executive Officer", "phase_prompt": ["According to the new user's task and some creative brainstorm ideas listed below: ", "Task: \"{task}\".", "Modality: \"{modality}\".", "Ideas: \"{ideas}\".", "We have decided to complete the task through a executable software implemented via a programming language. ", "As the {assistant_role}, to satisfy the new user's demand and make the software realizable, you should propose a concrete programming language. If python can complete this task via Python, please answer Python; otherwise, answer another programming language (e.g., Java, C++, etc,).", "Note that we must ONLY discuss the target programming language and do not discuss anything else! Once we all have expressed our opinion(s) and agree with the results of the discussion unanimously, any of us must actively terminate the discussion and conclude the best programming language we have discussed without any other words or reasons, return only one line using the format: \"<INFO> *\" where \"*\" represents a programming language."]}, "Coding": {"assistant_role_name": "Programmer", "user_role_name": "Chief Technology Officer", "phase_prompt": ["According to the new user's task and our software designs listed below: ", "Task: \"{task}\".", "Task description: \"{description}\".", "Modality: \"{modality}\".", "Programming Language: \"{language}\"", "Ideas:\"{ideas}\"", "We have decided to complete the task through a executable software with multiple files implemented via {language}. As the {assistant_role}, to satisfy the new user's demands, you should write one or multiple files and make sure that every detail of the architecture is, in the end, implemented as code. {gui}", "Think step by step and reason yourself to the right decisions to make sure we get it right.", "You will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.", "Then you will output the content of each file including complete code. Each file must strictly follow a markdown code block format, where the following tokens must be replaced such that \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" is the lowercase file name including the file extension, \"LANGUAG<PERSON>\" in the programming language, \"DOCSTRING\" is a string literal specified in source code that is used to document a specific segment of code, and \"CODE\" is the original code:", "FILENAME", "```LANGUAGE", "'''", "DOCSTRING", "'''", "CODE", "```", "You will start with the \"main\" file, then go to the ones that are imported by that file, and so on.", "Please note that the code should be fully functional. Ensure to implement all functions. No placeholders (such as 'pass' in Python)."]}, "ArtDesign": {"assistant_role_name": "Programmer", "user_role_name": "Chief Creative Officer", "phase_prompt": ["Our developed source codes and corresponding test reports are listed below: ", "Task: \"{task}\".", "Programming Language: \"{language}\"", "Source Codes:", "\"{codes}\"", "Note that each file must strictly follow a markdown code block format, where the following tokens must be replaced such that \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" is the lowercase file name including the file extension, \"LANGUAGE\" in the programming language, \"DOCSTRING\" is a string literal specified in source code that is used to document a specific segment of code, and \"CODE\" is the original code:", "FILENAME", "```LANGUAGE", "'''", "DOCSTRING", "'''", "CODE", "```", "As the {assistant_role}, to satisfy the new user's demand and equip the software with a beautiful graphical user interface (GUI), we will discuss and design many decorative images for GUI decoration. Now, we keep discussing the GUI beautification by listing some functionally independent elements in GUI that are being considered to be decorated by different pictures. For example, ten digits (0-9) in a calculator are functionally independent.", "To answer, use the format: \" FILENAME.png: DESCRIPTION\" where \"FILENAME\" is the filename of the image and \"DESCRIPTION\" denotes the detailed description of the independent elements. For example:", "'''", "button_1.png: The button with the number \"1\" on it.", "button_multiply.png: The button with the multiplication symbol (\"*\") on it.", "background.png: the background color to decorate the Go game", "'''", "Now, list all functionally independent elements as much as possible."]}, "ArtIntegration": {"assistant_role_name": "Programmer", "user_role_name": "Chief Creative Officer", "phase_prompt": ["Our developed source codes and corresponding test reports are listed below: ", "Task: \"{task}\".", "Programming Language: \"{language}\"", "Source Codes:", "\"{codes}\"", "Note that each file must strictly follow a markdown code block format, where the following tokens must be replaced such that \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" is the lowercase file name including the file extension, \"LANGUAGE\" in the programming language, \"DOCSTRING\" is a string literal specified in source code that is used to document a specific segment of code, and \"CODE\" is the original code:", "FILENAME", "```LANGUAGE", "'''", "DOCSTRING", "'''", "CODE", "```", "As the {assistant_role}, to satisfy the new user's demand and equip the software with a beautiful graphical user interface (GUI), you will incorporate our designed images for GUI decoration. Here are some ready-made high-quality pictures and corresponding descriptions:", "{images}", "Note that the designed images have a fixed size of 256x256 pixels and the images are located in the same directory as all the Python files; please dynamically scaling these images according to the size of GUI, and use \"self.*\" to avoid displaying-related problems caused by automatic garbage collection. For example:", "```", "self.image = ImageTk.PhotoImage(Image.open(\"./image.png\").resize((50, 50)))", "```", "Now, use some or all of the pictures into the GUI to make it more beautiful and creative. Output codes strictly following the required format mentioned above."]}, "CodeComplete": {"assistant_role_name": "Programmer", "user_role_name": "Chief Technology Officer", "phase_prompt": ["According to the new user's task and our software designs listed below: ", "Task: \"{task}\".", "Modality: \"{modality}\".", "Programming Language: \"{language}\"", "Codes:", "\"{codes}\"", "Unimplemented File:", "\"{unimplemented_file}\"", "In our software, each file must strictly follow a markdown code block format, where the following tokens must be replaced such that \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" is the lowercase file name including the file extension, \"LANGUAGE\" in the programming language, \"DOCSTRING\" is a string literal specified in source code that is used to document a specific segment of code, and \"CODE\" is the original code:", "FILENAME", "```LANGUAGE", "'''", "DOCSTRING", "'''", "CODE", "```", "As the {assistant_role}, to satisfy the complete function of our developed software, you have to implement all methods in the {unimplemented_file} file which contains a unimplemented class. Now, implement all methods of the {unimplemented_file} and all other codes needed, then output the fully implemented codes, strictly following the required format."]}, "CodeReviewComment": {"assistant_role_name": "Code Reviewer", "user_role_name": "Programmer", "phase_prompt": ["According to the new user's task and our software designs: ", "Task: \"{task}\".", "Modality: \"{modality}\".", "Programming Language: \"{language}\"", "Ideas: \"{ideas}\"", "Codes:", "\"{codes}\"", "As the {assistant_role}, to make the software directly operable without further coding, ChatDev have formulated the following regulations:", "1) all referenced classes should be imported;", "2) all methods should be implemented;", "3) all methods need to have the necessary comments;", "4) no potential bugs;", "5) The entire project conforms to the tasks proposed by the user;", "6) most importantly, do not only check the errors in the code, but also the logic of code. Make sure that user can interact with generated software without losing any feature in the requirement;", "Now, you should check the above regulations one by one and review the codes in detail, propose one comment with the highest priority about the codes, and give me instructions on how to fix. Tell me your comment with the highest priority and corresponding suggestions on revision. If the codes are perfect and you have no comment on them, return only one line like \"<INFO> Finished\"."]}, "CodeReviewModification": {"assistant_role_name": "Programmer", "user_role_name": "Code Reviewer", "phase_prompt": ["According to the new user's task, our designed product modality, languages and ideas, our developed first-edition source codes are listed below: ", "Task: \"{task}\".", "Modality: \"{modality}\".", "Programming Language: \"{language}\"", "Ideas: \"{ideas}\"", "Codes: ", "\"{codes}\"", "Comments on Codes:", "\"{comments}\"", "In the software, each file must strictly follow a markdown code block format, where the following tokens must be replaced such that \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" is the lowercase file name including the file extension, \"LANGUAGE\" in the programming language, \"DOCSTRING\" is a string literal specified in source code that is used to document a specific segment of code, and \"CODE\" is the original code. Format:", "FILENAME", "```LANGUAGE", "'''", "DOCSTRING", "'''", "CODE", "```", "As the {assistant_role}, to satisfy the new user's demand and make the software creative, executive and robust, you should modify corresponding codes according to the comments. Then, output the full and complete codes with all bugs fixed based on the comments. Return all codes strictly following the required format."]}, "TestErrorSummary": {"assistant_role_name": "Programmer", "user_role_name": "Software Test Engineer", "phase_prompt": ["Our developed source codes and corresponding test reports are listed below: ", "Programming Language: \"{language}\"", "Source Codes:", "\"{codes}\"", "Test Reports of Source Codes:", "\"{test_reports}\"", "According to my test reports, please locate and summarize the bugs that cause the problem."]}, "TestModification": {"assistant_role_name": "Programmer", "user_role_name": "Software Test Engineer", "phase_prompt": ["Our developed source codes and corresponding test reports are listed below: ", "Programming Language: \"{language}\"", "Source Codes:", "\"{codes}\"", "Test Reports of Source Codes:", "\"{test_reports}\"", "Error Summary of Test Reports:", "\"{error_summary}\"", "Note that each file must strictly follow a markdown code block format, where the following tokens must be replaced such that \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" is the lowercase file name including the file extension, \"LANGUAGE\" in the programming language, \"DOCSTRING\" is a string literal specified in source code that is used to document a specific segment of code, and \"CODE\" is the original code:", "FILENAME", "```LANGUAGE", "'''", "DOCSTRING", "'''", "CODE", "```", "As the {assistant_role}, to satisfy the new user's demand and make the software execute smoothly and robustly, you should modify the codes based on the error summary. Now, use the format exemplified above and modify the problematic codes based on the error summary. Output the codes that you fixed based on the test reported and corresponding explanations (strictly follow the format defined above, including FILENAME, LANGUAGE, DOCSTRING and CODE; incomplete \"TODO\" codes are strictly prohibited). If no bugs are reported, please return only one line like \"<INFO> Finished\"."]}, "EnvironmentDoc": {"assistant_role_name": "Programmer", "user_role_name": "Chief Technology Officer", "phase_prompt": ["The new user's task and our developed codes are listed: ", "Task: \"{task}\".", "Modality: \"{modality}\".", "Programming Language: \"{language}\"", "Ideas: \"{ideas}\"", "Codes: ", "\"{codes}\"", "As the {assistant_role}, you should write a requirements.txt file, which is commonly used in Python projects to specify the dependencies or packages required for the project to run properly. It serves as a way to document and manage the project's dependencies in a standardized format. For example:", "requirements.txt", "```", "numpy==1.19.2", "pandas>=1.1.4", "```", "According to the codes and file format listed above, write a requirements.txt file to specify the dependencies or packages required for the project to run properly."]}, "Manual": {"assistant_role_name": "Chief Product Officer", "user_role_name": "Chief Executive Officer", "phase_prompt": ["The new user's task, our developed codes and required dependencies are listed: ", "Task: \"{task}\".", "Modality: \"{modality}\".", "Programming Language: \"{language}\"", "Ideas: \"{ideas}\"", "Codes: ", "\"{codes}\"", "Requirements:", "\"{requirements}\"", "As the {assistant_role}, by using <PERSON><PERSON>, you should write a manual.md file which is a detailed user manual to use the software, including introducing main functions of the software, how to install environment dependencies and how to use/play it. For example:", "manual.md", "```", "# <PERSON><PERSON><PERSON><PERSON>", "Building applications with LLMs through composability", "Looking for the JS/TS version? Check out LangChain.js.", "**Production Support:** As you move your LangChains into production, we'd love to offer more comprehensive support.", "Please fill out this form and we'll set up a dedicated support Slack channel.", "## Quick Install", "`pip install langchain`", "or", "`conda install langchain -c conda-forge`", "## 🤔 What is this?", "Large language models (LLMs) are emerging as a transformative technology, enabling developers to build applications that they previously could not. However, using these LLMs in isolation is often insufficient for creating a truly powerful app - the real power comes when you can combine them with other sources of computation or knowledge.", "This library aims to assist in the development of those types of applications. Common examples of these applications include:", "**❓ Question Answering over specific documents**", "- Documentation", "- End-to-end Example: Question Answering over Notion Database", "**🤖 Agents**", "- Documentation", "- End-to-end Example: GPT+WolframAlpha", "## 📖 Documentation", "Please see [here](https://python.langchain.com) for full documentation on:", "- Getting started (installation, setting up the environment, simple examples)", "- How-To examples (demos, integrations, helper functions)", "- Reference (full API docs)", "- Resources (high-level explanation of core concepts)", "```"]}}