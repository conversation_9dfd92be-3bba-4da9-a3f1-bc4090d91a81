/* ChatDev Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.logo h1 {
    color: #4a5568;
    margin-bottom: 5px;
}

.logo p {
    color: #718096;
    font-size: 14px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-indicators {
    display: flex;
    gap: 30px;
}

.indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #4a5568;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e53e3e;
}

.dot.online {
    background: #38a169;
    animation: pulse 2s infinite;
}

.dot.offline {
    background: #e53e3e;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.count {
    background: #4299e1;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
    height: calc(100vh - 140px);
}

/* Office Panel Styles */
.office-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.office-panel h2 {
    color: #4a5568;
    margin-bottom: 20px;
    text-align: center;
}

.office-container {
    height: calc(100% - 60px);
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.office-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 15px;
    height: 100%;
}

/* Workstation Styles */
.workstation {
    background: white;
    border-radius: 12px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.workstation:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.workstation.working {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    animation: working 2s infinite;
}

@keyframes working {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.agent-avatar {
    font-size: 32px;
    margin-bottom: 8px;
}

.agent-info {
    text-align: center;
}

.agent-name {
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 4px;
}

.agent-status {
    font-size: 12px;
    color: #718096;
    padding: 2px 8px;
    border-radius: 8px;
    background: #edf2f7;
}

.agent-status.working {
    background: #fed7d7;
    color: #c53030;
}

.work-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e2e8f0;
    transition: all 0.3s ease;
}

.work-indicator.active {
    background: #48bb78;
    animation: pulse 1.5s infinite;
}

/* Control Panel Styles */
.control-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.task-form-section, .tasks-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.task-form-section h2, .tasks-section h2 {
    color: #4a5568;
    margin-bottom: 15px;
}

/* Form Styles */
.task-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-weight: 600;
    color: #4a5568;
    font-size: 14px;
}

.form-group input, .form-group textarea, .form-group select {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    outline: none;
    border-color: #4299e1;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.submit-btn {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.submit-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Tasks Container */
.tasks-container {
    max-height: 300px;
    overflow-y: auto;
}

.task-item {
    background: #f7fafc;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #4299e1;
    transition: all 0.3s ease;
}

.task-item:hover {
    background: #edf2f7;
}

.task-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.task-name {
    font-weight: 600;
    color: #2d3748;
}

.task-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.task-status.queued { background: #fed7d7; color: #c53030; }
.task-status.running { background: #feebc8; color: #dd6b20; }
.task-status.completed { background: #c6f6d5; color: #38a169; }
.task-status.failed { background: #fed7d7; color: #e53e3e; }

.task-progress {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    margin: 8px 0;
}

.task-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #3182ce);
    transition: width 0.3s ease;
}

.no-tasks {
    text-align: center;
    color: #718096;
    padding: 40px 20px;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #718096;
}

.close:hover {
    color: #4a5568;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #4299e1;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.toast.success { border-left-color: #38a169; }
.toast.error { border-left-color: #e53e3e; }
.toast.warning { border-left-color: #dd6b20; }

/* Office Management Link */
.office-management-link {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

.office-management-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }

    .office-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .status-indicators {
        justify-content: center;
    }

    .office-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(7, 1fr);
    }

    .office-management-link {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px auto;
        display: block;
        text-align: center;
        width: fit-content;
    }
}
