/* Office Management Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.logo h1 {
    color: #4a5568;
    margin-bottom: 5px;
}

.logo p {
    color: #718096;
    font-size: 14px;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    gap: 10px;
}

.tab-btn {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.tab-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.tab-btn.active {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    border-color: #3182ce;
}

/* Tab Content */
.tab-content {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    min-height: 600px;
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.section-header h2 {
    color: #2d3748;
    font-size: 24px;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Workers Grid */
.workers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.worker-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.worker-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: #4299e1;
}

.worker-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.worker-avatar {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: #f7fafc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #e2e8f0;
}

.worker-info h3 {
    color: #2d3748;
    margin-bottom: 5px;
}

.worker-role {
    color: #718096;
    font-size: 14px;
}

.worker-details {
    margin-bottom: 15px;
}

.worker-mbti {
    display: inline-block;
    background: #edf2f7;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 10px;
}

.worker-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.skill-tag {
    background: #e6fffa;
    color: #234e52;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 11px;
}

.worker-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #feebc8;
    color: #c05621;
}

.btn-delete {
    background: #fed7d7;
    color: #c53030;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #718096;
}

.close:hover {
    color: #4a5568;
}

/* Form Styles */
.worker-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-weight: 600;
    color: #4a5568;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4299e1;
}

/* Office Designer */
.office-designer {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 20px;
    height: 600px;
}

.workstation-palette {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
}

.workstation-palette h3 {
    color: #2d3748;
    margin-bottom: 15px;
}

.palette-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.palette-item {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: grab;
    transition: all 0.3s ease;
}

.palette-item:hover {
    border-color: #4299e1;
    transform: translateY(-1px);
}

.palette-item.dragging {
    opacity: 0.5;
    cursor: grabbing;
}

.item-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.item-name {
    font-size: 12px;
    color: #4a5568;
    font-weight: 600;
}

.office-canvas {
    background: white;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.canvas-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 2px;
    height: 100%;
    min-height: 500px;
}

.grid-cell {
    background: #f7fafc;
    border: 1px dashed #e2e8f0;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.grid-cell:hover {
    background: #edf2f7;
}

.grid-cell.occupied {
    background: #4299e1;
    border-color: #3182ce;
}

/* Office Controls */
.office-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.office-controls select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    background: white;
}

/* Templates Grid */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.template-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.template-category {
    background: #e6fffa;
    color: #234e52;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Automation Dashboard */
.automation-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.active-tasks,
.task-history {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
}

.active-tasks h3,
.task-history h3 {
    color: #2d3748;
    margin-bottom: 15px;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #4299e1;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.toast.success { border-left-color: #38a169; }
.toast.error { border-left-color: #e53e3e; }
.toast.warning { border-left-color: #dd6b20; }

/* Responsive Design */
@media (max-width: 1024px) {
    .office-designer {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .automation-dashboard {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .workers-grid {
        grid-template-columns: 1fr;
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
    }
}
