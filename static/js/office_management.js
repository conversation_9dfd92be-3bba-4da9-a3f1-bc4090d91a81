// Office Management JavaScript
class OfficeManager {
    constructor() {
        this.socket = io();
        this.workers = new Map();
        this.officeLayout = null;
        this.taskTemplates = new Map();
        this.currentTab = 'workers';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSocketListeners();
        this.loadInitialData();
        this.initializeOfficeCanvas();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Add worker modal
        const addWorkerBtn = document.getElementById('add-worker-btn');
        const addWorkerModal = document.getElementById('add-worker-modal');
        const closeWorkerModal = document.getElementById('close-worker-modal');
        const addWorkerForm = document.getElementById('add-worker-form');

        addWorkerBtn.addEventListener('click', () => {
            addWorkerModal.classList.add('show');
        });

        closeWorkerModal.addEventListener('click', () => {
            addWorkerModal.classList.remove('show');
        });

        addWorkerForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.createWorker();
        });

        // Office theme change
        const officeTheme = document.getElementById('office-theme');
        if (officeTheme) {
            officeTheme.addEventListener('change', (e) => {
                this.changeOfficeTheme(e.target.value);
            });
        }

        // Workstation palette drag and drop
        this.setupWorkstationPalette();
    }

    setupSocketListeners() {
        this.socket.on('connect', () => {
            console.log('Connected to office management server');
        });

        this.socket.on('worker_created', (worker) => {
            this.addWorkerToGrid(worker);
            this.showToast(`${worker.name} has been hired!`, 'success');
        });

        this.socket.on('office_updated', (layout) => {
            this.updateOfficeLayout(layout);
        });
    }

    async loadInitialData() {
        try {
            // Load workers
            const workersResponse = await fetch('/api/workers');
            const workers = await workersResponse.json();
            workers.forEach(worker => this.addWorkerToGrid(worker));

            // Load office layout
            const layoutResponse = await fetch('/api/office/layout');
            const layout = await layoutResponse.json();
            this.updateOfficeLayout(layout);

            // Load task templates
            const templatesResponse = await fetch('/api/templates');
            const templates = await templatesResponse.json();
            templates.forEach(template => this.addTemplateToGrid(template));

        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
    }

    async createWorker() {
        const formData = new FormData(document.getElementById('add-worker-form'));
        const workerData = {
            name: formData.get('name') || document.getElementById('worker-name').value,
            role: formData.get('role') || document.getElementById('worker-role').value,
            department: formData.get('department') || document.getElementById('worker-department').value,
            mbti_type: formData.get('mbti') || document.getElementById('worker-mbti').value,
            avatar_emoji: formData.get('avatar') || document.getElementById('worker-avatar').value,
            skills: (formData.get('skills') || document.getElementById('worker-skills').value).split(',').map(s => s.trim()),
            experience_level: formData.get('experience') || document.getElementById('worker-experience').value,
            communication_style: formData.get('communication') || document.getElementById('worker-communication').value
        };

        try {
            const response = await fetch('/api/workers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(workerData)
            });

            if (response.ok) {
                const worker = await response.json();
                this.addWorkerToGrid(worker);
                document.getElementById('add-worker-modal').classList.remove('show');
                document.getElementById('add-worker-form').reset();
                this.showToast(`${worker.name} has been hired!`, 'success');
            } else {
                const error = await response.json();
                this.showToast(error.error || 'Failed to create worker', 'error');
            }
        } catch (error) {
            console.error('Error creating worker:', error);
            this.showToast('Network error occurred', 'error');
        }
    }

    addWorkerToGrid(worker) {
        this.workers.set(worker.id, worker);
        
        const workersGrid = document.getElementById('workers-grid');
        const workerCard = document.createElement('div');
        workerCard.className = 'worker-card';
        workerCard.id = `worker-${worker.id}`;

        workerCard.innerHTML = `
            <div class="worker-header">
                <div class="worker-avatar">${worker.avatar_emoji}</div>
                <div class="worker-info">
                    <h3>${worker.name}</h3>
                    <div class="worker-role">${worker.role}</div>
                </div>
            </div>
            <div class="worker-details">
                <div class="worker-mbti">${worker.personality.mbti_type}</div>
                <div class="worker-department">📍 ${worker.department}</div>
                <div class="worker-skills">
                    ${worker.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                </div>
                <div class="worker-experience">Experience: ${worker.experience_level}</div>
            </div>
            <div class="worker-actions">
                <button class="btn-small btn-edit" onclick="officeManager.editWorker('${worker.id}')">✏️ Edit</button>
                <button class="btn-small btn-delete" onclick="officeManager.deleteWorker('${worker.id}')">🗑️ Remove</button>
            </div>
        `;

        workersGrid.appendChild(workerCard);
    }

    async editWorker(workerId) {
        const worker = this.workers.get(workerId);
        if (!worker) return;

        // Pre-fill the form with worker data
        document.getElementById('worker-name').value = worker.name;
        document.getElementById('worker-role').value = worker.role;
        document.getElementById('worker-department').value = worker.department;
        document.getElementById('worker-mbti').value = worker.personality.mbti_type;
        document.getElementById('worker-avatar').value = worker.avatar_emoji;
        document.getElementById('worker-skills').value = worker.skills.join(', ');
        document.getElementById('worker-experience').value = worker.experience_level;
        document.getElementById('worker-communication').value = worker.personality.communication_style;

        // Show modal
        document.getElementById('add-worker-modal').classList.add('show');
        
        // Change form to edit mode
        const form = document.getElementById('add-worker-form');
        form.dataset.editId = workerId;
        form.querySelector('button[type="submit"]').textContent = '💾 Update Worker';
    }

    async deleteWorker(workerId) {
        const worker = this.workers.get(workerId);
        if (!worker) return;

        if (confirm(`Are you sure you want to remove ${worker.name} from your team?`)) {
            try {
                const response = await fetch(`/api/workers/${workerId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    this.workers.delete(workerId);
                    document.getElementById(`worker-${workerId}`).remove();
                    this.showToast(`${worker.name} has been removed from the team`, 'success');
                } else {
                    this.showToast('Failed to remove worker', 'error');
                }
            } catch (error) {
                console.error('Error deleting worker:', error);
                this.showToast('Network error occurred', 'error');
            }
        }
    }

    initializeOfficeCanvas() {
        const canvasGrid = document.getElementById('canvas-grid');
        if (!canvasGrid) return;

        // Create grid cells
        for (let i = 0; i < 96; i++) { // 12x8 grid
            const cell = document.createElement('div');
            cell.className = 'grid-cell';
            cell.dataset.index = i;
            cell.addEventListener('click', (e) => this.handleCellClick(e));
            canvasGrid.appendChild(cell);
        }
    }

    setupWorkstationPalette() {
        const paletteItems = document.querySelectorAll('.palette-item');
        paletteItems.forEach(item => {
            item.draggable = true;
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', e.target.dataset.type);
                e.target.classList.add('dragging');
            });
            item.addEventListener('dragend', (e) => {
                e.target.classList.remove('dragging');
            });
        });

        const canvasGrid = document.getElementById('canvas-grid');
        if (canvasGrid) {
            canvasGrid.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            canvasGrid.addEventListener('drop', (e) => {
                e.preventDefault();
                const workstationType = e.dataTransfer.getData('text/plain');
                this.addWorkstationToCanvas(workstationType, e.target);
            });
        }
    }

    handleCellClick(e) {
        const cell = e.target;
        if (cell.classList.contains('occupied')) {
            // Show workstation details
            this.showWorkstationDetails(cell);
        }
    }

    addWorkstationToCanvas(type, targetCell) {
        if (!targetCell.classList.contains('grid-cell')) return;

        const index = parseInt(targetCell.dataset.index);
        const row = Math.floor(index / 12);
        const col = index % 12;

        // Mark cells as occupied (simplified - just mark the clicked cell)
        targetCell.classList.add('occupied');
        targetCell.innerHTML = this.getWorkstationIcon(type);
        targetCell.dataset.workstationType = type;

        this.showToast(`${type.replace('_', ' ')} added to office`, 'success');
    }

    getWorkstationIcon(type) {
        const icons = {
            'desk': '🖥️',
            'executive_office': '👔',
            'creative_space': '🎨',
            'meeting_room': '🤝',
            'research_lab': '🔬',
            'break_room': '☕'
        };
        return icons[type] || '📦';
    }

    changeOfficeTheme(theme) {
        const canvas = document.getElementById('office-canvas');
        if (canvas) {
            canvas.className = `office-canvas theme-${theme}`;
            this.showToast(`Office theme changed to ${theme}`, 'success');
        }
    }

    updateOfficeLayout(layout) {
        this.officeLayout = layout;
        // Update the visual representation
        console.log('Office layout updated:', layout);
    }

    addTemplateToGrid(template) {
        this.taskTemplates.set(template.id, template);
        
        const templatesGrid = document.getElementById('templates-grid');
        if (!templatesGrid) return;

        const templateCard = document.createElement('div');
        templateCard.className = 'template-card';
        templateCard.innerHTML = `
            <div class="template-header">
                <h3>${template.name}</h3>
                <span class="template-category">${template.category.replace('_', ' ')}</span>
            </div>
            <p>${template.description}</p>
            <div class="template-meta">
                <small>Duration: ${template.estimated_duration}</small>
                <small>Complexity: ${template.complexity}</small>
            </div>
            <button class="btn-primary" onclick="officeManager.useTemplate('${template.id}')">
                🚀 Use Template
            </button>
        `;

        templatesGrid.appendChild(templateCard);
    }

    async useTemplate(templateId) {
        const template = this.taskTemplates.get(templateId);
        if (!template) return;

        // Redirect to main dashboard with template pre-selected
        window.location.href = `/?template=${templateId}`;
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);

        // Remove on click
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }
}

// Initialize office manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.officeManager = new OfficeManager();
});

// Add some personality-based interactions
function addPersonalityEffects() {
    // Add floating thoughts/ideas from workers
    setInterval(() => {
        const workers = document.querySelectorAll('.worker-card');
        if (workers.length > 0 && Math.random() < 0.1) {
            const randomWorker = workers[Math.floor(Math.random() * workers.length)];
            createThoughtBubble(randomWorker);
        }
    }, 5000);
}

function createThoughtBubble(workerCard) {
    const thoughts = [
        "💡 I have an idea!",
        "🤔 Let me think about this...",
        "✨ This could work better...",
        "📊 The data shows...",
        "🎯 We should focus on...",
        "🚀 What if we try..."
    ];

    const bubble = document.createElement('div');
    bubble.style.cssText = `
        position: absolute;
        background: rgba(66, 153, 225, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 15px;
        font-size: 12px;
        pointer-events: none;
        z-index: 1000;
        animation: thoughtFloat 3s ease-out forwards;
    `;
    bubble.textContent = thoughts[Math.floor(Math.random() * thoughts.length)];

    const rect = workerCard.getBoundingClientRect();
    bubble.style.left = rect.left + 'px';
    bubble.style.top = (rect.top - 40) + 'px';

    document.body.appendChild(bubble);

    // Remove bubble after animation
    setTimeout(() => {
        if (bubble.parentNode) {
            bubble.parentNode.removeChild(bubble);
        }
    }, 3000);
}

// Add CSS for thought bubble animation
const style = document.createElement('style');
style.textContent = `
    @keyframes thoughtFloat {
        0% {
            opacity: 0;
            transform: translateY(20px) scale(0.8);
        }
        50% {
            opacity: 1;
            transform: translateY(-10px) scale(1);
        }
        100% {
            opacity: 0;
            transform: translateY(-40px) scale(0.9);
        }
    }
`;
document.head.appendChild(style);

// Initialize personality effects
setTimeout(addPersonalityEffects, 2000);
