// ChatDev Dashboard JavaScript
class ChatDevDashboard {
    constructor() {
        this.socket = io();
        this.tasks = new Map();
        this.agents = new Map();
        this.init();
    }

    init() {
        this.setupSocketListeners();
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupSocketListeners() {
        // Connection status
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            this.showToast('Connected to ChatDev server', 'success');
        });

        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            this.showToast('Disconnected from server', 'error');
        });

        // Task events
        this.socket.on('task_created', (task) => {
            this.addTask(task);
            this.showToast(`Task "${task.project_name}" created`, 'success');
        });

        this.socket.on('task_updated', (task) => {
            this.updateTask(task);
        });

        this.socket.on('phase_update', (data) => {
            this.updatePhase(data);
        });

        // Agent status updates
        this.socket.on('agent_status_update', (data) => {
            this.updateAgentStatus(data.agent, data.status, data.task_id);
        });

        this.socket.on('agents_status', (agentsStatus) => {
            Object.entries(agentsStatus).forEach(([agent, status]) => {
                this.updateAgentStatus(agent, status.status, status.current_task);
            });
        });
    }

    setupEventListeners() {
        // Task form submission
        const taskForm = document.getElementById('new-task-form');
        taskForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitTask();
        });

        // Modal close
        const closeModal = document.getElementById('close-modal');
        closeModal.addEventListener('click', () => {
            this.closeModal();
        });

        // Click outside modal to close
        const modal = document.getElementById('progress-modal');
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    async loadInitialData() {
        try {
            const response = await fetch('/api/tasks');
            const tasks = await response.json();
            tasks.forEach(task => this.addTask(task));
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.showToast('Failed to load tasks', 'error');
        }
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const dot = statusElement.querySelector('.dot');
        const text = statusElement.querySelector('span:last-child');
        
        if (connected) {
            dot.classList.remove('offline');
            dot.classList.add('online');
            text.textContent = 'Connected';
        } else {
            dot.classList.remove('online');
            dot.classList.add('offline');
            text.textContent = 'Disconnected';
        }
    }

    async submitTask() {
        const form = document.getElementById('new-task-form');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('.submit-btn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        // Show loading state
        submitBtn.disabled = true;
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline';

        try {
            const response = await fetch('/api/tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    project_name: formData.get('project_name'),
                    description: formData.get('description'),
                    config: formData.get('config')
                })
            });

            if (response.ok) {
                const result = await response.json();
                form.reset();
                this.showToast('Task created successfully!', 'success');
            } else {
                const error = await response.json();
                this.showToast(error.error || 'Failed to create task', 'error');
            }
        } catch (error) {
            console.error('Error submitting task:', error);
            this.showToast('Network error occurred', 'error');
        } finally {
            // Reset button state
            submitBtn.disabled = false;
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }
    }

    addTask(task) {
        this.tasks.set(task.id, task);
        this.renderTask(task);
        this.updateActiveTasksCount();
    }

    updateTask(task) {
        this.tasks.set(task.id, task);
        this.renderTask(task);
        
        if (task.status === 'completed') {
            this.showToast(`Project "${task.project_name}" completed!`, 'success');
        } else if (task.status === 'failed') {
            this.showToast(`Project "${task.project_name}" failed`, 'error');
        }
    }

    renderTask(task) {
        const container = document.getElementById('tasks-container');
        const noTasks = document.getElementById('no-tasks');
        
        // Hide "no tasks" message
        if (noTasks) {
            noTasks.style.display = 'none';
        }

        let taskElement = document.getElementById(`task-${task.id}`);
        if (!taskElement) {
            taskElement = document.createElement('div');
            taskElement.id = `task-${task.id}`;
            taskElement.className = 'task-item';
            taskElement.addEventListener('click', () => this.showTaskDetails(task.id));
            container.appendChild(taskElement);
        }

        taskElement.innerHTML = `
            <div class="task-header">
                <div class="task-name">${task.project_name}</div>
                <div class="task-status ${task.status}">${task.status.toUpperCase()}</div>
            </div>
            <div class="task-description">${task.description.substring(0, 100)}${task.description.length > 100 ? '...' : ''}</div>
            <div class="task-progress">
                <div class="task-progress-fill" style="width: ${task.progress || 0}%"></div>
            </div>
            <div class="task-meta">
                <small>Created: ${new Date(task.created_at).toLocaleString()}</small>
                ${task.current_phase ? `<small>Phase: ${task.current_phase}</small>` : ''}
            </div>
        `;
    }

    updateAgentStatus(agentName, status, taskId) {
        const agentKey = agentName.toLowerCase().replace(' ', '');
        const statusElement = document.getElementById(`${agentKey}-status`);
        const indicatorElement = document.getElementById(`${agentKey}-indicator`);
        const workstationElement = document.querySelector(`[data-agent="${agentName}"]`);

        if (statusElement) {
            statusElement.textContent = status === 'working' ? 'Working' : 'Idle';
            statusElement.className = `agent-status ${status}`;
        }

        if (indicatorElement) {
            if (status === 'working') {
                indicatorElement.classList.add('active');
            } else {
                indicatorElement.classList.remove('active');
            }
        }

        if (workstationElement) {
            if (status === 'working') {
                workstationElement.classList.add('working');
            } else {
                workstationElement.classList.remove('working');
            }
        }

        this.agents.set(agentName, { status, taskId });
    }

    showTaskDetails(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        const modal = document.getElementById('progress-modal');
        const title = document.getElementById('modal-title');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const currentPhase = document.getElementById('current-phase');

        title.textContent = `${task.project_name} - Development Progress`;
        progressFill.style.width = `${task.progress || 0}%`;
        progressText.textContent = `${task.progress || 0}%`;
        currentPhase.textContent = task.current_phase || task.status;

        modal.style.display = 'flex';
    }

    closeModal() {
        const modal = document.getElementById('progress-modal');
        modal.style.display = 'none';
    }

    updateActiveTasksCount() {
        const activeTasks = Array.from(this.tasks.values()).filter(
            task => task.status === 'running' || task.status === 'queued'
        );
        const countElement = document.querySelector('#active-tasks .count');
        countElement.textContent = activeTasks.length;
    }

    updatePhase(data) {
        const task = this.tasks.get(data.task_id);
        if (task) {
            task.current_phase = data.phase;
            task.progress = data.progress;
            this.updateTask(task);
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);

        // Remove on click
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatDevDashboard();
});

// Add some office ambiance effects
function addOfficeEffects() {
    // Add floating particles effect
    const officeContainer = document.querySelector('.office-container');
    
    setInterval(() => {
        if (Math.random() < 0.1) { // 10% chance every interval
            createFloatingParticle(officeContainer);
        }
    }, 2000);
}

function createFloatingParticle(container) {
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(66, 153, 225, 0.6);
        border-radius: 50%;
        pointer-events: none;
        left: ${Math.random() * 100}%;
        top: 100%;
        animation: float 3s ease-out forwards;
    `;

    container.appendChild(particle);

    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 3000);
}

// Add CSS for floating animation
const style = document.createElement('style');
style.textContent = `
    @keyframes float {
        to {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize office effects
setTimeout(addOfficeEffects, 1000);
